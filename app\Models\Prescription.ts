import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column, HasMany, hasMany, Has<PERSON><PERSON>, hasOne } from '@ioc:Adonis/Lucid/Orm'
import Diagnostic from './Diagnostic'
import Patient from './Patient'
import Soignant from './Soignant'
import PrescriptionItem from './PrescriptionItem'
import QuotationRequest from './QuotationRequest'
import Order from './Order'

export default class Prescription extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'code_ref' })
  public codeRef: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column({ columnName: 'name' })
  public name: string | null

  @column({ columnName: 'diagnostic_id' })
  public diagnosticId: number | null

  @column({ columnName: 'pro_id' })
  public proId: number | null

  @column({ columnName: 'patient_id' })
  public patientId: number

  @column({ columnName: 'reason' })
  public reason: string

  @column({ columnName: 'used_insurance' })
  public usedInsurance: boolean

  @column({ columnName: 'is_ordered' })
  public isOrdered: boolean

  @column({ columnName: 'created_by_self' })
  public createdBySelf: boolean

  @column.dateTime({ columnName: 'validated_at' })
  public validatedAt: DateTime | null

  @column.dateTime({ columnName: 'blocked_at' })
  public blockedAt: DateTime | null

  @column({ columnName: 'image' })
  public image: string | null

  @column({ columnName: 'payment_status' })
  public paymentStatus: string

  @column({ columnName: 'requested_at' })
  public requestedAt: string | null

  @column({ columnName: 'payment_date' })
  public paymentDate: string | null

  @column({ columnName: 'is_requested' })
  public isRequested: boolean

  @belongsTo(() => Diagnostic, {
    foreignKey: 'diagnosticId',
    localKey: 'id'
  })
  public diagnostic: BelongsTo<typeof Diagnostic>

  @belongsTo(() => Patient, {
    foreignKey: 'patientId',
    localKey: 'id'
  })
  public patient: BelongsTo<typeof Patient>

  @belongsTo(() => Soignant, {
    foreignKey: 'proId',
    localKey: 'id'
  })
  public pro: BelongsTo<typeof Soignant>

  @hasMany(() => PrescriptionItem, {
    foreignKey: 'prescriptionId',
    localKey: 'id'
  })
  public items: HasMany<typeof PrescriptionItem>

  @hasOne(() => QuotationRequest, {
    foreignKey: 'prescriptionId',
    localKey: 'id'
  })
  public quotation_requests: HasOne<typeof QuotationRequest>

  @hasMany(() => Order, {
    foreignKey: 'prescriptionId',
    localKey: 'id'
  })
  public orders: HasMany<typeof Order>

  @hasMany(() => Order, {
    foreignKey: 'prescriptionId',
    localKey: 'id',
    onQuery(query) {
      query.orderBy('created_at', 'desc').first();
    },
  })
  public lastOrder: HasMany<typeof Order>

}

