import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import CategoryDiagnostic from './CategoryDiagnostic'
import PathologyCategory from './PathologyCategory'

export default class Pathology extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public name: string

  @column()
  public description: string | null

  @column({columnName: 'category_diagnostic_id'})
  public categoryDiagnosticId: number

  @column({columnName: 'pathology_category_id'})
  public pathologyCategoryId: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => CategoryDiagnostic, {
    foreignKey: 'categoryDiagnosticId',
    localKey: 'id'
  })
  public category_diagnostic: BelongsTo<typeof CategoryDiagnostic>

  @belongsTo(() => PathologyCategory, {
    foreignKey: 'pathologyCategoryId',
    localKey: 'id'
  })
  public pathology_category: BelongsTo<typeof PathologyCategory>
}
