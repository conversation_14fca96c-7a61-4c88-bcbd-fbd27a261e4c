import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import CategoryAnalyze from './CategoryAnalyze'

export default class AnalyzeType extends BaseModel {

  public static table = 'analyse_types'

  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'name' })
  public name: string

  @column({ columnName: 'slug' })
  public slug: string

  @column({ columnName: 'description' })
  public description: string

  @column({ columnName: 'category_id' })
  public category_id: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => CategoryAnalyze,{
    foreignKey: 'category_id'
  })
  public category: BelongsTo<typeof CategoryAnalyze>
}
