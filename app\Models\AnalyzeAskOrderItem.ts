import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class AnalyzeAskOrderItem extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column({ columnName: 'analyze_ask_order_id' })
  public analyzeAskOrderId: number

  @column({ columnName: 'analyze_ask_item_id' })
  public analyzeAskItemId: number

  @column({ columnName: 'comment' })
  public comment: string

  @column({ columnName: 'is_paid' })
  public isPaid: boolean
}
