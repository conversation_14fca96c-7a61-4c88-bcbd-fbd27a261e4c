import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class SnapshotProduct extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'package_snapshot_id' })
  public packageSnapshotId: number

  @column({ columnName: 'product_id' })
  public productId: number

  @column({ columnName: 'public_price' })
  public publicPrice: number

  @column()
  public quantity: number

  @column({ columnName: 'is_active' })
  public isActive: boolean

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
