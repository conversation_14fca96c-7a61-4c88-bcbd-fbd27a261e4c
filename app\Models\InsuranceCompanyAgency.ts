import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class InsuranceCompanyAgency extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column({ columnName: 'name' })
  public name: string

  @column({ columnName: 'insurance_company_id' })
  public insuranceCompanyId: number

  @column({ columnName: 'agency_code' })
  public agencyCode: string | null

  @column({ columnName: 'address' })
  public address: string | null

  @column({ columnName: 'email' })
  public email: string | null

  @column({ columnName: 'phone' })
  public phone: string | null

  @column({ columnName: 'is_principal' })
  public isPrincipal: boolean

  @column({ columnName: 'is_rural' })
  public isRural: boolean
}
