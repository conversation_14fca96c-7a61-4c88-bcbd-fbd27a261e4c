import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import { belongsTo } from '@ioc:Adonis/Lucid/Orm'
import InsuranceCompany from './InsuranceCompany'
import Laboratory from './Laboratory'

export default class InsuranceCompanyLaboratory extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'insurance_company_id' })
  public insuranceCompanyId: number

  @column({ columnName: 'laboratory_id' })
  public laboratoryId: number

  @column({ columnName: 'is_active' })
  public isActive: boolean

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => InsuranceCompany,{
    localKey: 'id',
    foreignKey: 'insuranceCompanyId'
  })
  public insuranceCompany: BelongsTo<typeof InsuranceCompany>

  @belongsTo(() => Laboratory,{
    localKey: 'id',
    foreignKey: 'laboratoryId'
  })
  public laboratory: BelongsTo<typeof Laboratory>
}
