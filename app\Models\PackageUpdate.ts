import { DateTime } from 'luxon';
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm';
import InsuranceYear from './InsuranceYear';
import Package from './Package';
import User from './User';

export default class PackageUpdate extends BaseModel {
  @column({ isPrimary: true })
  public id: number;

  @column()
  public packageId: number;

  @column()
  public insuranceYearId: number;

  @column()
  public currentVersion: string | null;

  @column()
  public newVersion: string | null;

  @column()
  public name: string | null;

  @column()
  public paymentType: 'monthly' | 'annual' | null;

  @column()
  public validity: number | null;

  @column()
  public taux: number | null;

  @column()
  public plafond: number | null;

  @column()
  public price: number | null;

  @column()
  public status: 'pending' | 'updated' | 'expired';

  @column({ columnName: 'type' })
  public type: 'individual' | 'team'

  @column({ columnName: 'visibility' })
  public visibility: 'public' | 'private'

  @column({ columnName: 'plafond_config' })
  public plafondConfig: any | null

  @column({ columnName: 'fees_config' })
  public feesConfig: any | null

  @column({ columnName: 'tranches_config' })
  public tranchesConfig: any | null

  @column.date()
  public startUpdate: DateTime | null;

  @column.date()
  public endUpdate: DateTime | null;

  @column()
  public products: any | null;

  @column()
  public analyzes: any | null;

  @column()
  public reason: string | null;

  @column()
  public createdBy: number | null;

  @column()
  public updatedBy: number | null;

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime;

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime;

  // Relations

  @belongsTo(() => Package, {
    foreignKey: 'packageId',
  })
  public package: BelongsTo<typeof Package>;

  @belongsTo(() => InsuranceYear, {
    foreignKey: 'insuranceYearId',
  })
  public insuranceYear: BelongsTo<typeof InsuranceYear>;

  @belongsTo(() => User, {
    foreignKey: 'createdBy',
  })
  public creator: BelongsTo<typeof User>;

  @belongsTo(() => User, {
    foreignKey: 'updatedBy',
  })
  public updater: BelongsTo<typeof User>;
}
