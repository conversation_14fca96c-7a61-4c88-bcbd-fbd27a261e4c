import Route from '@ioc:Adonis/Core/Route';
import InsuranceController from 'App/Controllers/Http/core/InsuranceController';

import MainController from 'App/Controllers/Http/core/MainController';
import MemberController from 'App/Controllers/Http/core/MemberController';

const mainCtrl = new MainController();
const memberCtrl = new MemberController();
const insuranceCtrl = new InsuranceController();

Route.group(() => {
  Route.group(() => {
    Route.get('/', async (ctx) => {
      return await mainCtrl.index(ctx)
    });
    Route.put('/update-team', async (ctx) => {
      return await mainCtrl.updateTeamInfo(ctx)
    });
    Route.get('/dashboard', async (ctx) => {
      return await mainCtrl.getDashboardData(ctx)
    });
    Route.get('/analytics', async (ctx) => {
      return await mainCtrl.getMembersAnalytics(ctx)
    });
    Route.get('/guarantee-types', async (ctx) => {
      return await mainCtrl.getGuaranteeTypes(ctx)
    });
    Route.get('/insurance-packages', async (ctx) => {
      return await mainCtrl.getInsurancePackages(ctx)
    });
  }).prefix('main');

  Route.group(() => {
    Route.get('/', async (ctx) => {
      return await memberCtrl.getTeamGroups(ctx)
    });
    Route.get('/details', async (ctx) => {
      return await memberCtrl.getTeamGroupDetails(ctx)
    });
    Route.post('/add', async (ctx) => {
      return await memberCtrl.addTeamGroup(ctx)
    });
  }).prefix('groups');

  Route.group(() => {
    Route.post('/add-member', async (ctx) => {
      return await memberCtrl.addMember(ctx)
    });
    Route.post('/update-member', async (ctx) => {
      return await memberCtrl.updateMember(ctx)
    });
    Route.post('/import', async (ctx) => {
      return await memberCtrl.importMembersV2(ctx)
    });
    Route.get('/', async (ctx) => {
      return await memberCtrl.getMembers(ctx)
    });
    Route.get('/details', async (ctx) => {
      return await memberCtrl.getMemberDetails(ctx)
    });
  }).prefix('members');
  Route.group(() => {
    Route.get('/companies', async (ctx) => {
      return await insuranceCtrl.getInsuranceCompanies(ctx)
    });
    Route.get('/contracts', async (ctx) => {
      return await insuranceCtrl.getInsuranceContracts(ctx)
    });
    Route.get('/active-contract', async (ctx) => {
      return await insuranceCtrl.getActiveContract(ctx)
    });
    Route.post('/subscribe', async (ctx) => {
      return await insuranceCtrl.subscribeToInsuranceCompany(ctx)
    });
    Route.group(() => {
      Route.post('/', async (ctx) => {
        return await insuranceCtrl.initPackageNegotiation(ctx)
      });
      Route.post('/reject', async (ctx) => {
        return await insuranceCtrl.rejectAndCounterPropose(ctx)
      });
      Route.post('/approve', async (ctx) => {
        return await insuranceCtrl.approveProposal(ctx)
      });

      Route.get('/', async (ctx) => {
        return await insuranceCtrl.getAllNegotiations(ctx)
      });
      Route.get('/details', async (ctx) => {
        return await insuranceCtrl.getNegotiationDetails(ctx)
      });
      Route.get('/history', async (ctx) => {
        return await insuranceCtrl.getNegotiationHistory(ctx)
      });
      Route.get('/versions', async (ctx) => {
        return await insuranceCtrl.getNegotiationVersions(ctx)
      });
      Route.get('/versions/details', async (ctx) => {
        return await insuranceCtrl.getNegotiationVersionsDetails(ctx)
      });
    }).prefix('negotiations');
  }).prefix('insurance');
}).prefix('api').namespace('App/Controllers/Http/core');
