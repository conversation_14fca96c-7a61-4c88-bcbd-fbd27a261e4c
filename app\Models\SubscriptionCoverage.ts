import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class SubscriptionCoverage extends BaseModel {

  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'subscription_id' })
  public subscriptionId: number

  @column({ columnName: 'package_snapshot_id' })
  public packageSnapshotId: number

  @column.date({ columnName: 'start_date' })
  public startDate: DateTime

  @column.date({ columnName: 'end_date' })
  public endDate: DateTime

  @column({ columnName: 'status' })
  public status: 'active' | 'expired' | 'terminated'

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
