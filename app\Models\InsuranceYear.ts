import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import InsuranceCompany from './InsuranceCompany'
import User from './User'
import { YearStatus } from 'App/Controllers/interfaces'

export default class InsuranceYear extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public uuid: string | null

  @column({ columnName: 'insurance_company_id' })
  public insuranceCompanyId: number

  @column()
  public libelle: string

  @column()
  public year: number

  @column({ columnName: 'type_year' })
  public typeYear: string

  @column.dateTime({ columnName: 'start_at' })
  public startAt: DateTime

  @column.dateTime({ columnName: 'end_at' })
  public endAt: DateTime

  @column.dateTime({ columnName: 'finished_at' })
  public finishedAt: DateTime

  @column({ columnName: 'adhesion_price' })
  public adhesionPrice: number

  @column({ columnName: 'adhesion_type' })
  public adhesionType: 'renewable' | 'unique' = 'renewable'

  @column({ columnName: 'adhesion_fee_required' })
  public adhesionFeeRequired: boolean

  @column({ columnName: 'beneficiary_config' })
  public beneficiaryConfig: any | null

  @column({ columnName: 'complementary_config' })
  public complementaryConfig: any | null

  @column({ columnName: 'refund_config' })
  public refundConfig: any | null

  @column()
  public status: YearStatus

  @column({ columnName: 'created_by' })
  public createdBy: number | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => InsuranceCompany, {
    foreignKey: 'insuranceCompanyId',
  })
  public insuranceCompany: BelongsTo<typeof InsuranceCompany>

  @belongsTo(() => User, {
    foreignKey: 'createdBy',
  })
  public creator: BelongsTo<typeof User>
}
