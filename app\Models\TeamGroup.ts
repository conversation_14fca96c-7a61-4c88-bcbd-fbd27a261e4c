import { DateTime } from 'luxon'
import { BaseModel, column, HasMany, hasMany } from '@ioc:Adonis/Lucid/Orm'
import TeamMember from './TeamMember'

export default class TeamGroup extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({columnName: 'public_id'})
  public publicId: string

  @column({columnName: 'name'})
  public name: string

  @column({columnName: 'code'})
  public code: string | null

  @column({columnName: 'description'})
  public description: string | null

  @column({columnName: 'team_id'})
  public teamId: number

  @column({columnName: 'is_default'})
  public isDefault: boolean

  @column({columnName: 'metadata'})
  public metadata: any | null

  @column({columnName: 'status'})
  public status: 'active' | 'inactive' = 'active'

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @hasMany(() => TeamMember, {
    localKey: 'id',
    foreignKey: 'teamGroupId',
  })
  public members: <PERSON><PERSON><PERSON><typeof TeamMember>
}
