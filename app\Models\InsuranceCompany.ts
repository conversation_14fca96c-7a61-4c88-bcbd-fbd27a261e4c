import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column, HasMany, hasMany, manyToMany } from '@ioc:Adonis/Lucid/Orm'
import InsuranceCompanyAgency from './InsuranceCompanyAgency'
import Package from './Package'
import Country from './Country'
import City from './City'
import InsuranceCompanyHospital from './InsuranceCompanyHospital'
import { ManyToMany } from '@ioc:Adonis/Lucid/Orm'
import InsuranceCompanyPharmacy from './InsuranceCompanyPharmacy'
import InsuranceCompanyLaboratory from './InsuranceCompanyLaboratory'

export default class InsuranceCompany extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({columnName: 'uuid'})
  public uuid: string

  @column()
  public name: string

  @column()
  public agrement: string

  @column()
  public email: string | null

  @column()
  public phone: string | null

  @column()
  public address: string | null

  @column()
  public seat: string | null

  @column({columnName: 'country_id'})
  public countryId: number

  @column({columnName: 'city_id'})
  public cityId: number

  @column()
  public website: string | null

  @column()
  public logo: string | null

  @column()
  public adhesionPrice: number | null

  @column()
  public taux: number | null

  @column({columnName: 'probation_duration'})
  public probationDuration: number | null

  @column()
  public status: string

  @column({columnName: 'description'})
  public description: string | null

  @column()
  public subscription_model: string | null

  @column()
  public type: string | null

  @column()
  public responsable: any | null

  @column()
  public configs: any | null

  @column()
  public certificat: any | null

  @column({columnName: 'plafond_config'})
  public plafondConfig: any | null

  @column({columnName: 'tranches_config'})
  public  tranches_config: any | null

  @column({columnName: 'active_refund'})
  public activeRefund: boolean | null

  @column({columnName: 'refund_config'})
  public refundConfig: any | null

  @column()
  public notes: string | null

  @column()
  public code: string 

  @column({columnName: 'contract_number_template'})
  public contractNumberTemplate: string

  @column({columnName: 'contract_number_settings'})
  public contractNumberSettings: any | null

  @column({columnName: 'contract_number_sequence'})
  public contractNumberSequence: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @hasMany(() => InsuranceCompanyAgency,{
    foreignKey: 'insuranceCompanyId',
    localKey: 'id'
  })
  public agencies: HasMany<typeof InsuranceCompanyAgency>

  @hasMany(() => Package,{
    foreignKey: 'insuranceCompanyId',
    localKey: 'id'
  })
  public packages: HasMany<typeof Package>

  @belongsTo(()=>Country,{
    foreignKey: 'countryId',
    localKey: 'id'
  })
  public country: BelongsTo<typeof Country>

  @belongsTo(()=>City,{
    foreignKey: 'cityId',
    localKey: 'id'
  })
  public city: BelongsTo<typeof City>

  @manyToMany(() => InsuranceCompanyHospital,{
    pivotTable: 'insurance_company_hospitals',
    pivotColumns: ['insurance_company_id', 'health_institute_id','is_active'],
    pivotForeignKey: 'insuranceCompanyId',
    pivotRelatedForeignKey: 'healthInstituteId',
    pivotTimestamps: true,
  })
  public hospitals: ManyToMany<typeof InsuranceCompanyHospital>
  
  
  @manyToMany(() => InsuranceCompanyPharmacy,{
    pivotTable: 'insurance_company_pharmacies',
    pivotColumns: ['insurance_company_id', 'pharmacy_id','is_active'],
    pivotForeignKey: 'insuranceCompanyId',
    pivotRelatedForeignKey: 'pharmacyId',
    pivotTimestamps: true,
  })
  public pharmacies: ManyToMany<typeof InsuranceCompanyPharmacy>
  
  @manyToMany(() => InsuranceCompanyLaboratory,{
    pivotTable: 'insurance_company_laboratories',
    pivotColumns: ['insurance_company_id', 'laboratory_id','is_active'],
    pivotForeignKey: 'insuranceCompanyId',
    pivotRelatedForeignKey: 'laboratoryId',
    pivotTimestamps: true,
  })
  public laboratories: ManyToMany<typeof InsuranceCompanyLaboratory>
}
