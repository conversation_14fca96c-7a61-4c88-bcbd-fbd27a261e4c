
import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import Package from './Package'
import Transaction from './Transaction'
import InsuranceCompany from './InsuranceCompany'
import InsuranceCompanyAgency from './InsuranceCompanyAgency'
import InsuranceCompanySubscription from './InsuranceCompanySubscription'

import Patient from './Patient'
import { InsuranceFeeStatus } from 'App/Controllers/interfaces'
import PatientInsuranceCompany from './PatientInsuranceCompany'

export default class InsuranceCompanyFee extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'patient_id' })
  public patient_id: number

  @column({ columnName: 'patient_insurance_company_id' })
  public patient_insurance_company_id: number

  @column({ columnName: 'insurance_company_id' })
  public insurance_company_id: number

  @column({ columnName: 'insurance_company_agency_id' })
  public agencyId: number | null

  @column({ columnName: 'insurance_company_subscription_id' })
  public insurance_company_subscription_id: number

  @column({ columnName: 'package_id' })
  public packageId: number

  @column({ columnName: 'transaction_id' })
  public transactionId: number

  @column({ columnName: 'reference' })
  public reference: string

  @column({ columnName: 'token' })
  public token: string

  @column({ columnName: 'amount_paid' })
  public amountPaid: number

  @column({ columnName: 'total_month' })
  public total_month: number

  @column({ columnName: 'is_valide' })
  public is_valide: boolean

  @column({ columnName: 'type_cotisation' })
  public type_cotisation: 'monthly' | 'yearly'

  @column({ columnName: 'amount_remaining' })
  public amountRemaining: number | null

  @column({ columnName: 'paid_by_tranche' })
  public paidByTranche: boolean

  @column({ columnName: 'nbre_tranches' })
  public nbreTranches: number | null


  @column({ columnName: 'tranches' })
  public tranches: any | null

  @column({ columnName: 'status' })
  public status: InsuranceFeeStatus

  @column.dateTime({ columnName: 'paid_at' })
  public paidAt: DateTime

  @column.dateTime({ columnName: 'start_at' })
  public startAt: DateTime

  @column.dateTime({ columnName: 'end_at' })
  public endAt: DateTime

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => Patient, {
    foreignKey: 'patient_id',
    localKey: 'id',
  })
  public patient: BelongsTo<typeof Patient>

  @belongsTo(() => PatientInsuranceCompany, {
    foreignKey: 'patient_insurance_company_id',
    localKey: 'id',
  })
  public patientInsurance: BelongsTo<typeof PatientInsuranceCompany>


  @belongsTo(() => Package, {
    foreignKey: 'packageId',
    localKey: 'id',
  })
  public package: BelongsTo<typeof Package>

  @belongsTo(() => Transaction, {
    foreignKey: 'transactionId',
    localKey: 'id',
  })
  public transaction: BelongsTo<typeof Transaction>

  @belongsTo(() => InsuranceCompany, {
    foreignKey: 'insurance_company_id',
    localKey: 'id',
  })
  public insurance_company: BelongsTo<typeof InsuranceCompany>

  @belongsTo(() => InsuranceCompanyAgency, {
    foreignKey: 'agencyId',
    localKey: 'id',
  })
  public agency: BelongsTo<typeof InsuranceCompanyAgency>

  @belongsTo(() => InsuranceCompanySubscription, {
    foreignKey: 'insurance_company_subscription_id',
    localKey: 'id',
  })
  public subscription: BelongsTo<typeof InsuranceCompanySubscription>
}
