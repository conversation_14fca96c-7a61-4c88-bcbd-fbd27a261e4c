import { DateTime } from 'luxon'
import { BaseModel, belongsTo, BelongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import CardOrder from './CardOrder'

export default class Card extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({columnName: 'card_order_id'})
  public cardOrderId: number

  @column()
  public uid: string

  @column({columnName: 'numcard'})
  public numcard: string | null

  @column({columnName: 'image_url'})
  public imageUrl: string | null

  @column()
  public type: 'online' | 'physical'

  @column({columnName: 'is_active'})
  public isActive: boolean

  @column({columnName: 'is_paid'})
  public isPaid: boolean

  @column({columnName: 'is_used'})
  public isUsed: boolean

  @column()
  public status: 'new' | 'used' | 'renew' | 'blocked'

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime


  @belongsTo(() => CardOrder, {
    foreignKey: 'cardOrderId',
    localKey: 'id'
  })
  public order: BelongsTo<typeof CardOrder>


}
