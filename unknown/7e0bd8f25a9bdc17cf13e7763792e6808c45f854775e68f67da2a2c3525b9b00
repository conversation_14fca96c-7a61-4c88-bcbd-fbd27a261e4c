import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class Monetization extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column()
  public userId: number

  @column()
  public code: string

  @column()
  public typePiece: 'CNI' | 'PASSEPORT' | 'PERMIS'

  @column()
  public pieceData: any | null

  @column()
  public paymentMethod: 'CARD' | 'CASH' | 'MOBILE'

  @column()
  public paymentData: any | null

  @column()
  public balance: any | null

  @column()
  public previousBalance: any | null

  @column()
  public status: 'PENDING' | 'ACTIVE' | 'INACTIVE' | 'BLOCKED' | 'CANCELED'

  @column()
  public previousPlan: any | null
}
