import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class CardOrder extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({columnName: 'libelle'})
  public libelle: string | null

  @column({columnName: 'description'})
  public description: string | null

  @column({ columnName: 'admin_id' })
  public adminId: number

  @column({columnName: 'total_quantity'})
  public totalQuantity: number

  @column({columnName: 'online_quantity'})
  public onlineQuantity: number

  @column({columnName: 'physical_quantity'})
  public physicalQuantity: number

  @column()
  public status: string

  @column.dateTime({ columnName: 'date_order' })
  public dateOrder: DateTime

  @column.dateTime({ columnName: 'date_canceled' })
  public dateCanceled: DateTime

  @column.dateTime({ columnName: 'date_completed' })
  public dateCompleted: DateTime

  @column()
  public stock: any

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
