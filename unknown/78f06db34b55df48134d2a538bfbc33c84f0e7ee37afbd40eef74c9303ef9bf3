import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import InsuranceCompany from './InsuranceCompany'
import Pharmacy from './Pharmacy'

export default class InsuranceCompanyPharmacy extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'insurance_company_id' })
  public insuranceCompanyId: number

  @column({ columnName: 'pharmacy_id' })
  public pharmacyId: number

  @column({ columnName: 'is_active' })
  public isActive: boolean

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => InsuranceCompany,{
    localKey: 'id',
    foreignKey: 'insuranceCompanyId'
  })
  public insuranceCompany: BelongsTo<typeof InsuranceCompany>

  @belongsTo(() => Pharmacy,{
    localKey: 'id',
    foreignKey: 'pharmacyId'
  })
  public pharmacy: BelongsTo<typeof Pharmacy>
}
