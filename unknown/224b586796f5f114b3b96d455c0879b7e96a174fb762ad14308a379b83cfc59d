import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column, <PERSON><PERSON><PERSON>, hasMany } from '@ioc:Adonis/Lucid/Orm'
import Patient from './Patient'
import InsuranceCompany from './InsuranceCompany'
import InsuranceCompanyAgency from './InsuranceCompanyAgency'
import InsuranceCompanySubscription from './InsuranceCompanySubscription'

export default class PatientInsuranceCompany extends BaseModel {
  @column({ isPrimary: true, columnName: 'id' })
  public id: number

  @column({ columnName: 'patient_id' })
  public patient_id: number

  @column({ columnName: 'insurance_company_id' })
  public insurance_company_id: number

  @column({ columnName: 'insurance_company_agency_id' })
  public agencyId: number | null

  @column({ columnName: 'code' })
  public code: string

  @column({ columnName: 'status' })
  public status: string

  @column({ columnName: 'type_client' })
  public type_client: string

  @column({ columnName: 'configs' })
  public configs: any

  @column({ columnName: 'docs' })
  public docs: any | null

  @column({ columnName: 'is_old' })
  public is_old: boolean

  @column({ columnName: 'is_dom' })
  public is_dom: boolean

  @column({ columnName: 'is_active' })
  public is_active: boolean

  @column({ columnName: 'is_pac' })
  public is_pac: boolean

  @column.dateTime({ columnName: 'probation_start_at' })
  public probation_start_at: DateTime

  @column.dateTime({ columnName: 'probation_end_at' })
  public probation_end_at: DateTime

  @column.dateTime({ columnName: 'validated_at' })
  public validated_at: DateTime

  @column.dateTime({ columnName: 'blocked_at' })
  public blocked_at: DateTime

  @column.dateTime({ columnName: 'archived_at' })
  public archived_at: DateTime

  @column.dateTime({ autoCreate: true, columnName: 'created_at' })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true, columnName: 'updated_at' })
  public updatedAt: DateTime

  @belongsTo(() => Patient,{
    foreignKey: 'patient_id',
    localKey: 'id',
  })
  public patient: BelongsTo<typeof Patient>

  @belongsTo(() => InsuranceCompany, {
    foreignKey: 'insurance_company_id',
    localKey: 'id',
  })
  public insurance_company: BelongsTo<typeof InsuranceCompany>

  @belongsTo(() => InsuranceCompanyAgency, {
    foreignKey: 'agencyId',
    localKey: 'id',
  })
  public agency: BelongsTo<typeof InsuranceCompanyAgency>

  @hasMany(() => InsuranceCompanySubscription, {
    foreignKey: 'patientId',
    localKey: 'id',
  })
  public subscriptions: HasMany<typeof InsuranceCompanySubscription>
}
