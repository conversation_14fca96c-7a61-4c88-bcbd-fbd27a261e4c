import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column, } from '@ioc:Adonis/Lucid/Orm'
import Patient from './Patient'
import Order from './Order'
import Prescription from './Prescription'
import AnalyzeAsk from './AnalyzeAsk'
import QuotationRequest from './QuotationRequest'
import QuotationProposal from './QuotationProposal'
import Pharmacy from './Pharmacy'
import Laboratory from './Laboratory'

export default class Delivery extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public reference: string | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column({ columnName: 'patient_id' })
  public patientId: number

  @column({ columnName: 'order_id' })
  public orderId: number | null

  @column({ columnName: 'prescription_id' })
  public prescriptionId: number | null

  @column({ columnName: 'analyze_ask_id' })
  public analyzeAskId: number | null

  @column({ columnName: 'quotation_request_id' })
  public quotationRequestId: number | null

  @column({ columnName: 'quotation_proposal_id' })
  public quotationProposalId: number

  @column({ columnName: 'pharmacy_id' })
  public pharmacyId: number | null

  @column({ columnName: 'laboratory_id' })
  public laboratoryId: number | null

  @column({ columnName: 'other_laboratory' })
  public otherLaboratory: string | null

  @column({ columnName: 'status' })
  public status: 'pending' | 'validated' | 'prepared' | 'in_progress' | 'delivered' | 'cancelled' | 'failed'

  @column({ columnName: 'delivery_type' })
  public deliveryType: 'prescription' | 'analyze'

  @column({ columnName: 'result_delivery_type' })
  public resultDeliveryType: 'internal' | 'external' = 'internal'

  @column({ columnName: 'analyze_receipt_file' })
  public analyzeReceiptFile: string | null

  @column({ columnName: 'metadata' })
  public metadata: any | null

  @column({ columnName: 'location' })
  public location: any | null

  @column({ columnName: 'price' })
  public price: number | null

  @column({ columnName: 'validated_at' })
  public validatedAt: DateTime | null

  @column({ columnName: 'prepared_at' })
  public preparedAt: DateTime | null

  @column({ columnName: 'date_delivery' })
  public dateDelivery: DateTime | null

  @belongsTo(() => Patient)
  public patient: BelongsTo<typeof Patient>

  @belongsTo(() => Order)
  public order: BelongsTo<typeof Order>

  @belongsTo(() => Prescription)
  public prescription: BelongsTo<typeof Prescription>

  @belongsTo(() => AnalyzeAsk)
  public analyzeAsk: BelongsTo<typeof AnalyzeAsk>

  @belongsTo(() => QuotationRequest)
  public quotationRequest: BelongsTo<typeof QuotationRequest>

  @belongsTo(() => QuotationProposal)
  public quotationProposal: BelongsTo<typeof QuotationProposal>

  @belongsTo(() => Pharmacy)
  public pharmacy: BelongsTo<typeof Pharmacy>

  @belongsTo(() => Laboratory)
  public laboratory: BelongsTo<typeof Laboratory>


}
