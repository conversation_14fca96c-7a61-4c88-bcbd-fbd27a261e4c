import Route from '@ioc:Adonis/Core/Route';
import AuthController from 'App/Controllers/Http/auth/AuthController';

const authCtrl = new AuthController();

Route.group(() => {
  Route.group(() => {
    Route.post('/login', async (ctx) => {
      return await authCtrl.login(ctx)
    });
    Route.post('/register-team', async (ctx) => {
      return await authCtrl.registerTeam(ctx)
    });

    Route.group(() => {
      Route.post('/logout', async (ctx) => {
        return await authCtrl.logout(ctx)
      });
    }).middleware('auth:api');
  }).prefix('auth');
}).prefix('api').namespace('App/Controllers/Http/auth');
