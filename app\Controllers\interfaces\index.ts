export interface ApiResponse {
  success: boolean
  message: string
  result: any
  except?: any
  errors?: any
}

export interface Address {
  libelle?: string,
  lat?: string,
  long?: string,
}

export interface Contact {
  name?: string;
  phone?: string;
  email?: string;
}

export interface PaymentOrder {
  amount: number;
  merchant_reference: string;
  description: string;
  callback_url?: string;
  redirect_url?: string;
  type_notif?: 'sms' | 'email';
  gateway_id: number;
  client: OrderClient;
}

export interface PaymentRequest {
  order_reference: string;
  amount: number;
  state: string;
  date_create: string;
  bill_url: string;
  code: string;
  merchant_reference: string;
  client: OrderClient;
  received_amount: number;
  callback_url: string;
  callback_type: null;
  callback_id: null;
  redirect_url: null;
  currency: string;
  ledger: null;
  payment_status: string;
  status: string;
  message: string;
  qrcode_url: string;
  payments_method: PaymentMethod[];
}

interface PaymentMethod {
  gateway: string;
  method: string;
  action: string;
  description: string;
  reference: string;
  id: number;
}

export interface OrderClient {
  lastname: string;
  firstname: string;
  email?: string;
  phone: string;
  city?: string;
  country?: string;
  address1?: string;
  address2?: string;
}

export interface PaymentResponse {
  state: 'Pending' | 'Error' | 'Paid' | 'Partial' | 'Excess';
  order_reference: string;
  merchant_reference: string;
  amount: number;
  currency: string;
  billed_amount: number;
  bill_url: string;
  date_update: string | null;
  date_expiration: string;
  client: {
    phone: string;
  };
  code_paiement: string;
  date_create: string;
  callback_status: string;
  payments: any[]; // ou spécifiez un type si vous avez plus d'informations
}

export interface CardStock {
  online_quantity: {
    initial: number,
    current: number,
  },
  physical_quantity: {
    initial: number,
    current: number,
  }
}

export interface HealthRecord {
  libelle: string;
  description?: string;
  creator?: string;
  created_at?: string;
  updated_at?: string;
}

// ... code existant ...

export interface PrescriptionStatus {
  prescriptionItem: number;
  quantityPrescribed: number;
  paidAt: string | null;
  quantityPaid: number;
  quantityRemaining: number;
  status: string;
}

export interface UserParrainage {
  plan: number
  activeMoney: boolean
  active_qrcode: number
  adhesion_fees: number
  create_account: number
}

export interface OldUser {
  id: number;
  admin_id: number;
  language_id: number;
  creator_id: number | null;
  country_id: number;
  blood_group_id: number;
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  username: string;
  password: string;
  role: 'patient' | 'soignant' | 'laborantin' | 'admin';
  gender: string;
  birthday_year: number;
  birthday_month: number;
  birthday_day: number;
  is_rural: boolean;
  is_isme: boolean;
  profession: string;
  lat: string | null;
  long: string | null;
  api_token: string;
  activated_at: Date;
  blocked_at: Date;
  created_at: Date;
  updated_at: Date;
  channel: string;
  carnet_is_active: boolean;
  login_key: string | null;
}

export enum UserStatus {
  Pending = 'pending',
  Actived = 'actived',
  Blocked = 'blocked',
  Deleted = 'deleted',
  Inactive = 'inactive',
  Archived = 'archived',
  SendIdentity = 'send_identity',
  ValidatedIdentity = 'validated_identity',
  RejectedIdentity = 'rejected_identity',
}

export enum AppointmentStatus {
  Pending = 'pending',
  Accepted = 'accepted',
  Rejected = 'rejected',
  Confirmed = 'confirmed',
  Paid = 'paid',
  Canceled = 'canceled',
}


export interface ICard {
  cardOrderId: number;
  uid: string;
  numcard: string | null;
  imageUrl: string | null;
  type: 'online' | 'physical';
  isActive: boolean;
  isPaid: boolean;
  isUsed: boolean;
  status: 'new' | 'used' | 'renew' | 'blocked';
}

export enum InsuranceFeeStatus {
  Pending = 'pending',
  Paid = 'paid',
  Completed = 'completed',
  Cancelled = 'cancelled',
}

export enum Gender {
  Male = 'M',
  Female = 'F',
}

export enum YearStatus {
  Pending = 'pending',
  Active = 'active',
  Started = 'started',
  Finished = 'finished',
  Expired = 'expired',
}

export enum PackageStatus {
  Draft = 'draft',
  Configured = 'configured',
  Published = 'published',
  Archived = 'archived',
}

export type VersionType = 'major' | 'minor' | 'patch';
export interface AutoIncrementOptions {
  /** Seuil maximum pour le patch avant d'incrémenter minor (défaut: 10) */
  patchLimit?: number;
  /** Seuil maximum pour le minor avant d'incrémenter major (défaut: 5) */
  minorLimit?: number;
}

export interface ProductToUpdate {
  action: 'add' | 'update' | 'remove';
  product_id: number;
  quantity?: number;
  public_price?: number;
}

export interface AnalyzeToUpdate {
  action: 'add' | 'update' |'remove';
  analyze_id: number;
  quantity?: number;
  public_price?: number;
}

