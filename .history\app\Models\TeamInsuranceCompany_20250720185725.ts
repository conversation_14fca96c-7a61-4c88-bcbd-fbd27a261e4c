import { DateTime } from 'luxon'
import { BaseModel, column, HasOne } from '@ioc:Adonis/Lucid/Orm'
import InsuranceCompany from './InsuranceCompany'
import InsuranceCompanyAgency from './InsuranceCompanyAgency'

export default class TeamInsuranceCompany extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'public_id' })
  public publicId: string

  @column({ columnName: 'team_id' })
  public teamId: number

  @column({ columnName: 'insurance_company_id' })
  public insuranceCompanyId: number

  @column({ columnName: 'insurance_company_agency_id' })
  public insuranceCompanyAgencyId: number | null

  @column()
  public code: string | null

  @column({ columnName: 'is_active' })
  public isActive: boolean = true

  @column()
  public docs: any | null

  @column()
  public status: 'pending' | 'validated' | 'blocked' | 'archived' = 'pending'

  @column({ columnName: 'validated_by' })
  public validatedBy: string | null

  @column.date({ columnName: 'validated_at' })
  public validatedAt: DateTime | null

  @column.date({ columnName: 'blocked_at' })
  public blockedAt: DateTime | null

  @column.date({ columnName: 'archived_at' })
  public archivedAt: DateTime | null

  @column()
  public metadata: any | null

  @column.dateTime({ columnName: 'deleted_at' })
  public deletedAt: DateTime | null

  @column.dateTime({ autoCreate: true, columnName: 'created_at' })
  public createdAt: DateTime | null

  @column.dateTime({ autoCreate: true, autoUpdate: true, columnName: 'updated_at' })
  public updatedAt: DateTime | null
}
