import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class AnalyzeAskResult extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column({ columnName: 'analyze_ask_id' })
  public analyzeAskId: number

  @column({ columnName: 'analyze_id' })
  public analyzeId: number

  @column({ columnName: 'diagnostic_id' })
  public diagnosticId: number

  @column({ columnName: 'pro_id' })
  public proId: number

  @column({ columnName: 'value_min' })
  public valueMin: number

  @column({ columnName: 'value_max' })
  public valueMax: number

  @column({ columnName: 'value' })
  public value: number

  @column({ columnName: 'is_positive' })
  public isPositive: boolean
}
