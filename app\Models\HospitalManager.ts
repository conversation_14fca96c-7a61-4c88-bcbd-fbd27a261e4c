import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import { BelongsTo } from '@ioc:Adonis/Lucid/Orm'
import HealthInstitute from './HealthInstitute'

export default class HospitalManager extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column({ columnName: 'user_id' })
  public userId: number

  @column({ columnName: 'last_name' })
  public lastName: string

  @column({ columnName: 'first_name' })
  public firstName: string

  @column({ columnName: 'phone' })
  public phone: string

  @column({ columnName: 'email' })
  public email: string

  @column({ columnName: 'address' })
  public address: string

  @column({ columnName: 'country_id' })
  public countryId: number

  @column({ columnName: 'city_id' })
  public cityId: number

  @column({ columnName: 'quarter_id' })
  public quarterId: number

  @column({ columnName: 'gender' })
  public gender: string

  @column({ columnName: 'birthday_year' })
  public birthdayYear: number

  @column({ columnName: 'birthday_month' })
  public birthdayMonth: number

  @column({ columnName: 'birthday_day' })
  public birthdayDay: number

  @column({ columnName: 'profession' })
  public profession: string

  @column({ columnName: 'health_institute_id' })
  public healthInstituteId: number

  @belongsTo(() => HealthInstitute, {
    foreignKey: 'healthInstituteId',
    localKey: 'id'
  })
  public healthInstitute: BelongsTo<typeof HealthInstitute>
}
