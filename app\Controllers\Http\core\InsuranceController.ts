import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { ApiResponse } from 'App/Controllers/interfaces';
import InsuranceCompany from 'App/Models/InsuranceCompany';
import InsuranceContract from 'App/Models/InsuranceContract';
import HelperController from '../helpers/HelperController';
import { schema } from '@ioc:Adonis/Core/Validator';
import InsuranceYear from 'App/Models/InsuranceYear';
import Package from 'App/Models/Package';
import Database from '@ioc:Adonis/Lucid/Database';
import cuid from 'cuid';
import ContractNumberGenerator from 'App/Services/ContractNumberGenerator';
import Negotiation from 'App/Models/Negotiation';
import NegotiationVersion from 'App/Models/NegotiationVersion';
import NegotiationPackage from 'App/Models/NegotiationPackage';
import GuaranteeType from 'App/Models/GuaranteeType';
// import { DateTime } from 'luxon';

export default class InsuranceController extends HelperController {

  public async getInsuranceCompanies({ response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
    }
    try {
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté. Veuillez vous authentifier pour accéder à cette ressource.";
        return response.status(401).json(apiResponse);
      }
      const companies = await InsuranceCompany.query().preload('packages');
      apiResponse.result = companies;
      apiResponse.success = true;
      return response.status(200).json(apiResponse);
    } catch (error) {
      apiResponse.message = "Une erreur est survenue lors de la récupération des informations de l'utilisateur, veuillez reessayer plus tard";
      apiResponse.except = error.message;
      return response.status(500).json(apiResponse);
    }
  }

  public async getInsuranceContracts({ response, auth, request }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
    }
    try {
      const page = request.input('page', 1);
      const limit = request.input('limit', 10);
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté. Veuillez vous authentifier pour accéder à cette ressource.";
        return response.status(401).json(apiResponse);
      }
      const team = await this.getTeamByManager(authUser.id);
      const contracts = await InsuranceContract.query().where('team_id', team.id).preload('insurance_company').preload('package').paginate(page, limit);
      apiResponse.result = contracts;
      apiResponse.message = "Contrats récupérés avec succès";
      apiResponse.success = true;
      return response.status(200).json(apiResponse);
    } catch (error) {
      apiResponse.message = "Une erreur est survenue lors de la récupération des informations de l'utilisateur, veuillez reessayer plus tard";
      apiResponse.except = error.message;
      return response.status(500).json(apiResponse);
    }
  }

  public async getActiveContract({ response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
    }
    try {
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté. Veuillez vous authentifier pour accéder à cette ressource.";
        return response.status(401).json(apiResponse);
      }
      const team = await this.getTeamByManager(authUser.id);
      const contract = await InsuranceContract.query().where('team_id', team.id).where('status', 'active').first();
      apiResponse.result = contract;
      apiResponse.message = "Contrat actif récupéré avec succès";
      apiResponse.success = true;
      return response.status(200).json(apiResponse);
    } catch (error) {
      apiResponse.message = "Une erreur est survenue lors de la récupération des informations de l'utilisateur, veuillez reessayer plus tard";
      apiResponse.except = error.message;
      return response.status(500).json(apiResponse);
    }
  }

  public async subscribeToInsuranceCompany({ response, auth, request }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
    }
    try {
      const payload = await request.validate({
        schema: schema.create({
          team_insurance_company_id: schema.number(),
          package_id: schema.number(),
          team_group_id: schema.number(),
        }),
      });
      const { team_insurance_company_id, package_id, team_group_id } = payload;
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté. Veuillez vous authentifier pour accéder à cette ressource.";
        return response.status(401).json(apiResponse);
      }
      const team = await this.getTeamByManager(authUser.id);

      const teamInsurance = await team.related('team_insurance_companies').query()
        .where('id', team_insurance_company_id)
        .where('status', 'pending')
        .where('is_active', false)
        .orderBy('created_at', 'desc')
        .preload('insurance_company')
        .first();
      if (!teamInsurance) {
        apiResponse.message = "Aucune assurance trouvée pour cette équipe";
        return response.status(400).json(apiResponse);
      }

      const insuranceCompany = teamInsurance.insurance_company
      let insuracePackage: Package | null = null;
      if (package_id) {
        insuracePackage = await Package.query()
          .where('id', package_id)
          .where('insurance_company_id', insuranceCompany.id)
          .where('type', 'team')
          .first();
      }
      const insuranceYear = await InsuranceYear.query().where('status', 'started').andWhere('insurance_company_id', insuranceCompany.id).first();
      if (!insuranceYear) {
        apiResponse.message = "Aucune année d'assurance active pour cette compagnie";
        return response.status(400).json(apiResponse);
      }

      const existingContract = await InsuranceContract.query()
        .where('team_id', team.id)
        .where('team_group_id', team_group_id)
        .where('team_insurance_company_id', team_insurance_company_id)
        .whereIn('status', ['draft', 'started'])
        .first();

      if (existingContract) {
        apiResponse.message = "Votre équipe a déjà un contrat en cours de traitement pour ce groupe";
        return response.status(400).json(apiResponse);
      }

      const trx = await Database.transaction();
      try {
        let team_code = team.teamCode;
        if (team_code.length < 3) {
          team_code = team_code.padEnd(3, '0');
        }
        const contractNumber = await ContractNumberGenerator.generate(insuranceCompany, team_code);
        const newContract = await InsuranceContract.create({
          publicId: cuid(),
          teamId: team.id,
          teamGroupId: team_group_id,
          teamInsuranceCompanyId: team_insurance_company_id,
          insuranceCompanyId: insuranceCompany.id,
          packageId: insuracePackage ? insuracePackage.id : null,
          insuranceYearId: insuranceYear.id,
          createdBy: authUser.id,
          status: 'draft',
          contractNumber: contractNumber,
        }, { client: trx });

        await trx.commit();
        apiResponse.success = true;
        apiResponse.message = "Souscription enregistrée avec succès";
        apiResponse.result = newContract;
        return response.status(201).json(apiResponse);
      } catch (error) {
        console.log("error subscribeToInsuranceCompany", error);
        await trx.rollback();
        apiResponse.message = "Echec de la souscription, une erreur serveur s'est produite";
        apiResponse.except = error;
        return response.status(500).json(apiResponse);
      }
    } catch (error) {
      console.log("error subscribeToInsuranceCompany", error);
      apiResponse.message = "Une erreur est survenue lors de la récupération des informations de l'utilisateur, veuillez reessayer plus tard";
      apiResponse.except = error.message;
      return response.status(500).json(apiResponse);
    }
  }

  // ==================== GESTION DES NÉGOCIATIONS ====================

  /**
   * 1. Initier une négociation de contrat (Entreprise)
   * L'entreprise crée une première proposition
   */
  public async initPackageNegotiation({ response, auth, request }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
    }
    try {
      const payload = await request.validate({
        schema: schema.create({
          team_group_id: schema.number(),
          insurance_company_id: schema.number(),
          annual_prime: schema.number(),
          family_settings: schema.object().members({
            max_dependents: schema.number(),
            partner_coverage: schema.boolean(),
            children_age_limit: schema.number(),
            principal_plafond: schema.number(),
            partner_plafond: schema.number(),
            child_plafond: schema.number(),
          }),
          guarantees: schema.array().members(schema.object().members({
            type: schema.number(),
            coverage_rate: schema.number(),
            annual_limit: schema.number(),
            comment: schema.string.optional(),
          })),
          coverage_period: schema.number(),
          resume: schema.string.optional(),
        }),
      });

      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté. Veuillez vous authentifier pour accéder à cette ressource.";
        return response.status(401).json(apiResponse);
      }

      const team = await this.getTeamByManager(authUser.id);
      const { team_group_id, insurance_company_id, annual_prime, family_settings, guarantees, coverage_period, resume } = payload;

      // Vérifier que le groupe appartient à l'équipe
      const group = await team.related('groups').query().where('id', team_group_id).first();
      if (!group) {
        apiResponse.message = "Groupe introuvable";
        return response.status(404).json(apiResponse);
      }
      const manager = await this.getManagerByUser(authUser.id);
      if (manager.teamId !== team.id) {
        apiResponse.message = "Vous n'êtes pas membre de cette équipe";
        return response.status(403).json(apiResponse);
      }

      const trx = await Database.transaction();
      try {
        // Générer la référence de négociation
        const reference = await this.generateNegotiationReference(insurance_company_id);

        // Récupérer la relation team_insurance_company
        const teamInsuranceCompany = await team.related('team_insurance_companies')
          .query()
          .where((query) => {
            query.where('is_active', true).orWhere('is_active', false);
          })
          .where((query) => {
            query.where('status', 'pending').orWhere('status', 'validated');
          })
          .first();

        if (!teamInsuranceCompany) {
          apiResponse.message = "Cette compagnie d'assurance n'est pas associée à votre équipe";
          return response.status(400).json(apiResponse);
        }

        // Créer la négociation
        const negotiation = await Negotiation.create({
          publicId: cuid(),
          reference: reference,
          teamId: team.id,
          teamGroupId: team_group_id,
          teamInsuranceCompanyId: teamInsuranceCompany.id,
          insuranceCompanyId: insurance_company_id,
          createdBy: manager.id,
          status: 'pending', // En attente de réponse de la compagnie
        }, { client: trx });

        // Générer la version de négociation
        const versionReference = await this.generateNegotiationVersionReference(negotiation.id);

        // Créer la première version (proposition de l'entreprise)
        const negotiationVersion = await NegotiationVersion.create({
          publicId: cuid(),
          negotiationId: negotiation.id,
          version: versionReference,
          actorType: 'team_member',
          actorId: manager.id,
          resume: resume,
          status: 'pending', // En attente de réponse
        }, { client: trx });

        // Créer le package de négociation
        await NegotiationPackage.create({
          negotiationVersionId: negotiationVersion.id,
          annualPrime: annual_prime,
          familySettings: family_settings,
          guarantees: guarantees,
          coveragePeriod: coverage_period,
        }, { client: trx });

        await trx.commit();

        apiResponse.success = true;
        apiResponse.message = "Négociation initiée avec succès";
        apiResponse.result = {
          negotiation,
          version: negotiationVersion,
          reference: reference
        };
        return response.status(201).json(apiResponse);

      } catch (error) {
        await trx.rollback();
        throw error;
      }

    } catch (error) {
      apiResponse.message = "Une erreur est survenue lors de l'initiation de la négociation";
      apiResponse.except = error.message;
      return response.status(500).json(apiResponse);
    }
  }

  /**
   * 2. Rejeter une proposition et faire une contre-proposition (Entreprise)
   * L'entreprise rejette la proposition de la compagnie et fait une nouvelle proposition
   */
  public async rejectAndCounterPropose({ response, auth, request }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
    }
    try {
      const payload = await request.validate({
        schema: schema.create({
          negotiation_id: schema.number(),
          version_id: schema.number(), // Version à rejeter
          annual_prime: schema.number(),
          family_settings: schema.object().members({
            max_dependents: schema.number(),
            partner_coverage: schema.boolean(),
            children_age_limit: schema.number(),
            principal_plafond: schema.number(),
            partner_plafond: schema.number(),
            child_plafond: schema.number(),
          }),
          guarantees: schema.array().members(schema.object().members({
            type: schema.number(),
            coverage_rate: schema.number(),
            annual_limit: schema.number(),
            comment: schema.string.optional(),
          })),
          coverage_period: schema.number(),
          coverage_start: schema.date.nullable(),
          coverage_end: schema.date.nullable(),

          resume: schema.string.optional(),
          rejection_reason: schema.string(),
        }),
      });

      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté. Veuillez vous authentifier pour accéder à cette ressource.";
        return response.status(401).json(apiResponse);
      }

      const { negotiation_id, version_id, annual_prime, family_settings, guarantees, coverage_period, resume, rejection_reason } = payload;

      // Vérifier que la négociation appartient à l'équipe de l'utilisateur
      const negotiation = await Negotiation.query()
        .where('id', negotiation_id)
        .preload('team')
        .first();

      if (!negotiation || negotiation.team.createdBy !== authUser.id) {
        apiResponse.message = "Négociation introuvable ou accès non autorisé";
        return response.status(404).json(apiResponse);
      }

      if (!negotiation) {
        apiResponse.message = "Négociation introuvable";
        return response.status(404).json(apiResponse);
      }

      const trx = await Database.transaction();
      try {
        // Rejeter la version de la compagnie
        await NegotiationVersion.query({ client: trx })
          .where('id', version_id)
          .update({
            status: 'rejected',
            rejectedBy: authUser.id,
            rejectedAt: new Date(),
          });

        // Générer la nouvelle version
        const versionReference = await this.generateNegotiationVersionReference(negotiation_id);

        // Créer la nouvelle proposition de l'entreprise
        const newVersion = await NegotiationVersion.create({
          publicId: cuid(),
          negotiationId: negotiation_id,
          version: versionReference,
          actorType: 'team_member',
          actorId: authUser.id,
          resume: resume,
          status: 'pending',
          metadata: { rejection_reason }
        }, { client: trx });

        // Créer le nouveau package
        await NegotiationPackage.create({
          negotiationVersionId: newVersion.id,
          annualPrime: annual_prime,
          familySettings: family_settings,
          guarantees: guarantees,
          coveragePeriod: coverage_period,
        }, { client: trx });

        await trx.commit();

        apiResponse.success = true;
        apiResponse.message = "Contre-proposition créée avec succès";
        apiResponse.result = {
          rejected_version_id: version_id,
          new_version: newVersion
        };
        return response.status(201).json(apiResponse);

      } catch (error) {
        await trx.rollback();
        throw error;
      }

    } catch (error) {
      apiResponse.message = "Une erreur est survenue lors de la création de la contre-proposition";
      apiResponse.except = error.message;
      return response.status(500).json(apiResponse);
    }
  }

  /**
   * 3. Approuver une proposition de la compagnie (Entreprise)
   * L'entreprise approuve la proposition de la compagnie, finalisant la négociation
   */
  public async approveProposal({ response, auth, request }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
    }
    try {
      const payload = await request.validate({
        schema: schema.create({
          negotiation_id: schema.number(),
          version_id: schema.number(), // Version à approuver
          approval_comment: schema.string.optional(),
        }),
      });

      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté. Veuillez vous authentifier pour accéder à cette ressource.";
        return response.status(401).json(apiResponse);
      }

      const { negotiation_id, version_id, approval_comment } = payload;

      // Vérifier que la négociation appartient à l'équipe de l'utilisateur
      const negotiation = await Negotiation.query()
        .where('id', negotiation_id)
        .preload('team')
        .first();

      if (!negotiation || negotiation.team.createdBy !== authUser.id) {
        apiResponse.message = "Négociation introuvable ou accès non autorisé";
        return response.status(404).json(apiResponse);
      }

      // Vérifier que la version existe et appartient à cette négociation
      const version = await NegotiationVersion.query()
        .where('id', version_id)
        .where('negotiation_id', negotiation_id)
        .where('status', 'accepted')
        .whereNotNull('accepted_at')
        .where((query) => {
          query
            .where('actor_type', 'insurance_manager')
            .orWhere('actor_type', 'team_member');
        })
        .first();

      if (!version) {
        apiResponse.message = "Version de négociation introuvable ou non éligible à l'approbation";
        return response.status(404).json(apiResponse);
      }

      const trx = await Database.transaction();
      try {
        // Approuver la version
        await NegotiationVersion.query({ client: trx })
          .where('id', version_id)
          .update({
            status: 'approved',
            isValidated: true,
            approvedAt: new Date(),
            metadata: { approval_comment }
          });

        // Mettre à jour le statut de la négociation
        await Negotiation.query({ client: trx })
          .where('id', negotiation_id)
          .update({
            status: 'approved',
            approvedBy: authUser.id,
            approvedAt: new Date(),
          });

        await trx.commit();

        apiResponse.success = true;
        apiResponse.message = "Proposition approuvée avec succès. La négociation est finalisée.";
        apiResponse.result = {
          negotiation_id,
          approved_version_id: version_id,
          status: 'approved'
        };
        return response.status(200).json(apiResponse);

      } catch (error) {
        await trx.rollback();
        throw error;
      }

    } catch (error) {
      apiResponse.message = "Une erreur est survenue lors de l'approbation de la proposition";
      apiResponse.except = error.message;
      return response.status(500).json(apiResponse);
    }
  }

  /**
   * 4. Obtenir l'historique d'une négociation (Entreprise)
   * Récupère toutes les versions et leur statut pour une négociation
   */
  public async getNegotiationHistory({ response, auth, request }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
    }
    try {
      const negotiation_id = request.input('negotiation_id');
      if (!negotiation_id) {
        apiResponse.message = "ID de négociation requis";
        return response.status(400).json(apiResponse);
      }

      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté. Veuillez vous authentifier pour accéder à cette ressource.";
        return response.status(401).json(apiResponse);
      }

      // Vérifier que la négociation appartient à l'équipe de l'utilisateur
      const negotiation = await Negotiation.query()
        .where('id', negotiation_id)
        .preload('team')
        .first();

      if (!negotiation || negotiation.team.createdBy !== authUser.id) {
        apiResponse.message = "Négociation introuvable ou accès non autorisé";
        return response.status(404).json(apiResponse);
      }

      // Récupérer toutes les versions avec leurs packages
      const versions = await NegotiationVersion.query()
        .where('negotiation_id', negotiation_id)
        .preload('negotiationPackage')
        .preload('team_member')
        .preload('manager')
        .orderBy('created_at', 'asc');

      const history = versions.map(version => ({
        id: version.id,
        version: version.version,
        actor_type: version.actorType,
        actor_id: version.actorId,
        status: version.status,
        is_validated: version.isValidated,
        resume: version.resume,
        created_at: version.createdAt,
        approved_at: version.approvedAt,
        rejected_at: version.rejectedAt,
        package: version.negotiationPackage,
        metadata: version.metadata,
        team_member: version.team_member,
        manager: version.manager
      }));

      apiResponse.success = true;
      apiResponse.message = "Historique de négociation récupéré avec succès";
      apiResponse.result = {
        negotiation: {
          id: negotiation.id,
          reference: negotiation.reference,
          status: negotiation.status,
          created_at: negotiation.createdAt,
          approved_at: negotiation.approvedAt,
          accepted_at: negotiation.acceptedAt,
          rejected_at: negotiation.rejectedAt
        },
        versions: history
      };
      return response.status(200).json(apiResponse);

    } catch (error) {
      apiResponse.message = "Une erreur est survenue lors de la récupération de l'historique";
      apiResponse.except = error.message;
      return response.status(500).json(apiResponse);
    }
  }

  public async getAllNegotiations({ response, auth, request }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
    }
    try {
      const team_insurance_company_id = request.input('team_insurance_company_id', null);
      const page = request.input('page', 1);
      const limit = request.input('limit', 10);
      const all = request.input('all', true);
      const status = request.input('status', null);
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté. Veuillez vous authentifier pour accéder à cette ressource.";
        return response.status(401).json(apiResponse);
      }

      const team = await this.getTeamByManager(authUser.id);

      const current_insurance_company = await team.related('team_insurance_companies')
        .query()
        .where((query) => {
          query.where('is_active', true).orWhere('is_active', false);
        })
        .where((query) => {
          query.where('status', 'pending').orWhere('status', 'validated');
        })
        .first();
      // console.log("current_insurance_company", current_insurance_company);
      if (!current_insurance_company) {
        apiResponse.message = "Compagnie d'assurance introuvable";
        return response.status(404).json(apiResponse);
      }


      let negotiationsQuery = Negotiation.query()
        .where('team_id', team.id);

      if (team_insurance_company_id) {
        negotiationsQuery = negotiationsQuery.where('team_insurance_company_id', team_insurance_company_id);
      } else if (current_insurance_company) {
        negotiationsQuery = negotiationsQuery.where('team_insurance_company_id', current_insurance_company.id);
      }
      // console.log("negotiationsQuery", negotiationsQuery);


      if (status) {
        negotiationsQuery = negotiationsQuery.where('status', status);
      }

      // Optimisation: Avoid duplicate code by chaining common operations
      negotiationsQuery = negotiationsQuery
        .orderBy('created_at', 'desc')
        .preload('team_group')
        .preload('team_insurance_company')
        .preload('insurance_company');

      const negotiations = all
        ? await negotiationsQuery
        : await negotiationsQuery.paginate(page, limit);

      // console.log("negotiations", negotiations.length);


      apiResponse.success = true;
      apiResponse.message = "Negotiations récupérées avec succès";
      apiResponse.result = negotiations;
      return response.status(200).json(apiResponse);

    } catch (error) {
      apiResponse.message = "Une erreur est survenue lors de la récupération des negotiations";
      apiResponse.except = error.message;
      return response.status(500).json(apiResponse);
    }
  }

  public async getNegotiationVersions({ response, auth, request }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
    }
    try {
      const negotiation_id = request.input('negotiation_id');
      const page = request.input('page', 1);
      const limit = request.input('limit', 10);
      const all = request.input('all', true);
      const status = request.input('status', null);
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté. Veuillez vous authentifier pour accéder à cette ressource.";
        return response.status(401).json(apiResponse);
      }
      if (!negotiation_id) {
        apiResponse.message = "ID de négociation requis";
        return response.status(400).json(apiResponse);
      }

      const team = await this.getTeamByManager(authUser.id);
      const negotiation = await Negotiation.query()
        .where('id', negotiation_id)
        .where('team_id', team.id)
        .first();

      if (!negotiation) {
        apiResponse.message = "Négociation introuvable";
        return response.status(404).json(apiResponse);
      }

      let versionsQuery = NegotiationVersion.query()
        .where('negotiation_id', negotiation.id).preload('negotiationPackage')

      // Appliquer le filtre de statut si spécifié
      if (status) {
        versionsQuery = versionsQuery.where('status', status);
      }

      // Appliquer la pagination ou récupérer toutes les versions
      const versions = all
        ? await versionsQuery
        : await versionsQuery.paginate(page, limit);

      apiResponse.success = true;
      apiResponse.message = "Versions de négociation récupérées avec succès";
      apiResponse.result = versions;
      return response.status(200).json(apiResponse);

    } catch (error) {
      apiResponse.message = "Une erreur est survenue lors de la récupération des versions de négociation";
      apiResponse.except = error.message;
      return response.status(500).json(apiResponse);
    }
  }

  public async getNegotiationDetails({ response, auth, request }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
    }
    try {
      const negotiation_id = request.input('negotiation_id');
      if (!negotiation_id) {
        apiResponse.message = "ID de négociation requis";
        return response.status(400).json(apiResponse);
      }
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté. Veuillez vous authentifier pour accéder à cette ressource.";
        return response.status(401).json(apiResponse);
      }
      const team = await this.getTeamByManager(authUser.id);
      const negotiation = await Negotiation.query()
        .where('id', negotiation_id)
        .where('team_id', team.id)
        .preload('team_group')
        .preload('team_insurance_company')
        .preload('insurance_company')
        .preload('creator', (query) => {
          query.preload('patient', (query) => {
            query.select(['id', 'first_name', 'last_name', 'phone', 'email']);
          });
        })
        .first();



      if (!negotiation) {
        apiResponse.message = "Négociation introuvable";
        return response.status(404).json(apiResponse);
      }

      const last_current_version = await NegotiationVersion.query()
        .where('negotiation_id', negotiation.id)
        .orderBy('created_at', 'desc')
        .preload('negotiationPackage')
        .first();

      apiResponse.success = true;
      apiResponse.message = "Détails de la négociation récupérés avec succès";
      apiResponse.result = {
        negotiation,
        last_current_version
      };
      return response.status(200).json(apiResponse);
    } catch (error) {
      apiResponse.message = "Une erreur est survenue lors de la récupération des détails de la négociation";
      apiResponse.except = error.message;
      return response.status(500).json(apiResponse);
    }
  }

  public async getNegotiationVersionsDetails({ response, auth, request }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
    }
    try {
      const version_id = request.input('version_id');
      if (!version_id || isNaN(Number(version_id))) {
        apiResponse.message = "ID de version requis et doit être un nombre valide";
        return response.status(400).json(apiResponse);
      }

      const versionIdNumber = Number(version_id);
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté. Veuillez vous authentifier pour accéder à cette ressource.";
        return response.status(401).json(apiResponse);
      }
      // Récupérer la version avec son package en une seule requête
      const version = await NegotiationVersion.query()
        .where('id', versionIdNumber)
        .preload('negotiation')
        .preload('team_member')
        .preload('manager')
        .preload('negotiationPackage')
        .first();

      if (!version) {
        apiResponse.message = "Version introuvable";
        return response.status(404).json(apiResponse);
      }

      const negotiation_package = version.negotiationPackage;
      const guarantees = negotiation_package?.guarantees;

      // Optimisation: Récupérer tous les types de garanties en une seule requête
      let formattedGuarantees: any[] = [];
      if (guarantees && guarantees.length > 0) {
        const guaranteeTypeIds = guarantees.map(g => g.type);
        const guaranteeTypes = await GuaranteeType.query()
          .whereIn('id', guaranteeTypeIds);

        // Créer un map pour un accès O(1)
        const guaranteeTypeMap = new Map(
          guaranteeTypes.map(type => [type.id, type])
        );

        formattedGuarantees = guarantees.map(guarantee => ({
          ...guarantee,
          type: guaranteeTypeMap.get(guarantee.type) || null,
          coverage_rate: guarantee.coverage_rate,
          annual_limit: guarantee.annual_limit,
          comment: guarantee.comment,
        }));
      }

      apiResponse.success = true;
      apiResponse.message = "Détails de la version récupérés avec succès";
      apiResponse.result = {
        ...version.toJSON(),
        negotiationPackage: negotiation_package ? {
          ...negotiation_package.toJSON(),
          guarantees: formattedGuarantees
        } : null
      };
      return response.status(200).json(apiResponse);
    } catch (error) {
      console.log("error getNegotiationVersionsDetails", error);
      apiResponse.message = "Une erreur est survenue lors de la récupération des détails de la version";
      apiResponse.except = error.message;
      return response.status(500).json(apiResponse);
    }
  }
}
