import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import HealthInstitute from './HealthInstitute'
import Soignant from './Soignant'

export default class Personal extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public soignant_id: number | null

  @column()
  public health_institute_id: number | null

  @column()
  public is_active: boolean

  @column()
  public is_principal: boolean

  @column()
  public role: string | null

  @column()
  public departement: string | null

  @column()
  public matricule: string | null

  @column()
  public note: string | null

  @column()
  public affiliation_date: DateTime | null

  @column()
  public end_date: DateTime | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => HealthInstitute, {
    foreignKey: 'health_institute_id',
  })
  public healthInstitute: BelongsTo<typeof HealthInstitute>

  @belongsTo(() => Soignant, {
    foreignKey: 'soignant_id',
  })
  public soignant: BelongsTo<typeof Soignant>
}
