import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import AnalyzeAsk from './AnalyzeAsk'
import Analyze from './Analyze'

export default class AnalyzeAskItem extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column({ columnName: 'analyze_ask_id' })
  public analyzeAskId: number

  @column({ columnName: 'analyze_id' })
  public analyzeId: number

  @column({ columnName: 'diagnostic_id' })
  public diagnosticId: number

  @column({ columnName: 'price' })
  public price: number

  @column({ columnName: 'justificatif' })
  public justificatif: string

  @column({ columnName: 'ca_certificat_id' })
  public ca_certificat_id: number

  @column({ columnName: 'can_be_ordered' })
  public can_be_ordered: boolean

  @column({ columnName: 'used_insurance' })
  public used_insurance: boolean

  @column({ columnName: 'medecin_conseil_id' })
  public medecin_conseil_id: number

  @column({ columnName: 'is_validated' })
  public is_validated: boolean

  @column.dateTime({ columnName: 'validated_at' })
  public validatedAt: DateTime | null

  @column.dateTime({ columnName: 'invalidated_at' })
  public invalidatedAt: DateTime | null

  @column({ columnName: 'is_ordered' })
  public isOrdered: boolean

  @column({ columnName: 'is_paid' })
  public isPaid: boolean

  @belongsTo(() => AnalyzeAsk, {
    foreignKey: 'analyzeAskId',
    localKey: 'id',
  })
  public analyzeAsk: BelongsTo<typeof AnalyzeAsk>

  @belongsTo(() => Analyze, {
    foreignKey: 'analyzeId',
    localKey: 'id',
  })
  public analyze: BelongsTo<typeof Analyze>
}
