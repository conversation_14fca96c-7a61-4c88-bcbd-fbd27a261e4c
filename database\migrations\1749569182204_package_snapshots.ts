import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'package_snapshots'

  public async up () {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.bigInteger('package_id')
      table.bigInteger('insurance_year_id').nullable().comment("Insurance year")
      table.bigInteger('insurance_company_id').nullable().comment("Insurance company")

      table.string('name').nullable()
      table.enum('type', ['individual', 'team']).nullable().defaultTo('individual')

      table.decimal('price', 18, 2).nullable().defaultTo(0)
      table.decimal('taux', 18, 2).nullable().defaultTo(0)
      table.decimal('plafond', 18, 2).nullable().defaultTo(0)

      table.json('fees_config').nullable()
      table.json('plafond_config').nullable()
      table.json('tranches_config').nullable()

      table.string('version').nullable().comment("version of package")
      table.string('checksum').comment("verification keys ")
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })

      table.index(['package_id', 'insurance_year_id'], 'package_snapshot_index')

      table.comment("Stocke les données générales du package au moment T .")
    })
  }

  public async down () {
    this.schema.dropTable(this.tableName)
  }
}
