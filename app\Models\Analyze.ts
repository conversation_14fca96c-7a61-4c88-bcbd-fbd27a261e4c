import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'


export default class Analyze extends BaseModel {
  public static connection = 'primary';
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'name' })
  public name: string

  @column({ columnName: 'eng_name' })
  public eng_name: string | null

  @column({ columnName: 'price' })
  public price: number | null

  @column({ columnName: 'type' })
  public type: string | null

  @column({ columnName: 'parent_id' })
  public parent_id: number | null

  @column({ columnName: 'analyse_type_id' })
  public analyse_type_id: number | null

  @column({ columnName: 'serie' })
  public serie: string | null

  @column({ columnName: 'unities' })
  public unities: string[] | null

  @column({ columnName: 'is_pack' })
  public is_pack: boolean | null

  @column({ columnName: 'is_pm' })
  public is_pm: boolean | null

  @column({ columnName: 'is_payable' })
  public is_payable: boolean

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

}
