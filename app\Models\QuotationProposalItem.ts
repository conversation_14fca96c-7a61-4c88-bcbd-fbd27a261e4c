import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import QuotationProposal from './QuotationProposal'
import PrescriptionItem from './PrescriptionItem'
import AnalyzeAskItem from './AnalyzeAskItem'

export default class QuotationProposalItem extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column({ columnName: 'quotation_proposal_id' })
  public quotationProposalId: number

  @column({ columnName: 'prescription_item_id' })
  public prescriptionItemId: number | null

  @column({ columnName: 'analyze_ask_item_id' })
  public analyzeAskItemId: number | null

  @column()
  public quantity: number

  @column({ columnName: 'unit_price' })
  public unitPrice?: number

  @column({ columnName: 'is_substitutable' })
  public isSubstitutable: boolean

  @column({ columnName: 'is_assured' })
  public isAssured: boolean

  @column()
  public status: 'paid' | 'partially_paid' | 'refused' | 'expired'

  @column({ columnName: 'quantity_paid' })
  public quantityPaid?: number

  @column({ columnName: 'paid_at' })
  public paidAt?: DateTime

  @column({ columnName: 'available_duration' })
  public availableDuration?: number

  @belongsTo(()=>QuotationProposal,{
    foreignKey: 'quotationProposalId',
    localKey: 'id'
  })
  public proposal: BelongsTo<typeof QuotationProposal>

  @belongsTo(()=>PrescriptionItem,{
    foreignKey: 'prescriptionItemId',
    localKey: 'id'
  })
  public prescription_items: BelongsTo<typeof PrescriptionItem>

  @belongsTo(()=>AnalyzeAskItem,{
    foreignKey: 'analyzeAskItemId',
    localKey: 'id'
  })
  public analyze_ask_items: BelongsTo<typeof AnalyzeAskItem>
}
