// import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext';

import Controller from '../Controller';
import TeamMember from 'App/Models/TeamMember';
import User from 'App/Models/User';
import Team from 'App/Models/Team';
import Database from '@ioc:Adonis/Lucid/Database';
import InsuranceCompany from 'App/Models/InsuranceCompany';

export default class HelperController extends Controller {

  public async getManagerByUser(userId: number): Promise<TeamMember> {
    const user = await User.find(userId);
    if (!user) throw new Error('User not found');
    const patient = await user.related('patient').query().firstOrFail();
    return await TeamMember.query()
      .where('patient_id', patient.id)
      .preload('team')
      .preload('patient')
      .firstOrFail();
  }

  public async getTeamByManager(userId: number): Promise<Team> {
    const manager = await this.getManagerByUser(userId);
    return await manager.related('team').query().firstOrFail();
  }

  public async generateWalletCode() {
    const gens = "**********";
    let length = 12;
    let code = '';
    for (let i = 0; i < length; i++) {
      code += gens.charAt(Math.floor(Math.random() * gens.length));
    }
    return code;
  }

  public async generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
      const r = (Math.random() * 16) | 0;
      const v = c === 'x' ? r : (r & 0x3) | 0x8;
      return v.toString(16);
    });
  }

  public async generateCodeParrainage(length: number) {
    const chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    let codeParrainage = '';
    for (let i = 0; i < length; i++) {
      codeParrainage += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return codeParrainage.toUpperCase();
  }

  public async generateNegotiationReference(insuranceCompanyId: number): Promise<string> {
    // Récupérer le code de la compagnie d'assurance
    const insuranceCompany = await InsuranceCompany.query()
      .where('id', insuranceCompanyId)
      .select('code')
      .firstOrFail();

    const companyCode = insuranceCompany.code;
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const dateStr = `${year}${month}${day}`;

    // Récupérer la dernière séquence pour cette compagnie et cette date
    const lastNegotiation = await Database
      .from('negotiations')
      .where('insurance_company_id', insuranceCompanyId)
      .where('reference', 'like', `NEG${companyCode}-${dateStr}-%`)
      .orderBy('id', 'desc')
      .first();

    let nextSequence = 1;

    if (lastNegotiation) {
      // Extraire la séquence de la dernière référence
      // Format: NEG{CODE}-{DATE}-{SEQUENCE}
      const lastReference = lastNegotiation.reference;
      const parts = lastReference.split('-');
      // parts[0] = "NEG{CODE}", parts[1] = "{DATE}", parts[2] = "{SEQUENCE}"
      const sequencePart = parts[parts.length - 1]; // Prendre le dernier élément
      const lastSequence = parseInt(sequencePart || '0');
      nextSequence = lastSequence + 1;
    }

    // Formater la séquence sur 6 chiffres avec des zéros de remplissage
    const formattedSequence = String(nextSequence).padStart(6, '0');

    // Générer la référence finale
    const reference = `NEG${companyCode}-${dateStr}-${formattedSequence}`;

    return reference;
  }

  public async generateNegotiationVersionReference(negotiationId: number): Promise<string> {
    // Récupérer la dernière version de la négociation
    const lastVersion = await Database
      .from('negotiation_versions')
      .where('negotiation_id', negotiationId)
      .orderBy('id', 'desc')
      .first();

    let nextVersion = 1;

    if (lastVersion) {
      // Extraire la version de la dernière version
      // Format: {VERSION}.{PATCH}
      const lastVersionString = lastVersion.version;
      const parts = lastVersionString.split('.');
      // parts[0] = "{VERSION}", parts[1] = "{PATCH}"
      const versionPart = parts[0];
      const lastVersionNumber = parseInt(versionPart || '0');
      nextVersion = lastVersionNumber + 1;
    }

    // Formater la version sur 2 chiffres avec des zéros de remplissage
    const formattedVersion = String(nextVersion).padStart(2, '0');

    // Générer la référence finale
    const reference = `${formattedVersion}.0`;

    return reference;
  }
}

