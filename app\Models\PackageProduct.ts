import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import Package from './Package'
import Product from './Product'

export default class PackageProduct extends BaseModel {

  public static connection = 'primary';

  @column({ isPrimary: true })
  public id: number

  @column({columnName: 'package_id'})
  public packageId: number

  @column({columnName: 'product_id'})
  public productId: number

  @column()
  public quantity: number

  @column({columnName: 'public_price'})
  public publicPrice: number | null

  @column({columnName: 'is_active'})
  public isActive: boolean

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => Package,{
    foreignKey: 'packageId',
    localKey: 'id',
  })
  public package: BelongsTo<typeof Package>

  @belongsTo(() => Product,{
    foreignKey: 'productId',
    localKey: 'id',
  })
  public product: BelongsTo<typeof Product>
}
