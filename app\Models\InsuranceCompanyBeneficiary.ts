import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo, BelongsTo } from '@ioc:Adonis/Lucid/Orm'
import InsuranceCompany from './InsuranceCompany'
import InsuranceCompanyAgency from './InsuranceCompanyAgency'
import Patient from './Patient'
import PatientInsuranceCompany from './PatientInsuranceCompany'

export default class InsuranceCompanyBeneficiary extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'insurance_company_id' })
  public insuranceCompanyId: number

  @column({ columnName: 'insurance_company_agency_id' })
  public insuranceCompanyAgencyId: number | null

  @column({ columnName: 'patient_id' })
  public patientId: number | null

  @column({ columnName: 'parent_id' })
  public parentId: number | null

  @column({ columnName: 'last_name' })
  public lastName: string

  @column({ columnName: 'first_name' })
  public firstName: string

  @column({ columnName: 'email' })
  public email: string | null

  @column({ columnName: 'phone' })
  public phone: string | null

  @column({ columnName: 'gender' })
  public gender: 'M' | 'F'

  @column({ columnName: 'birthday_year' })
  public birthdayYear: number | null

  @column({ columnName: 'birthday_month' })
  public birthdayMonth: number | null

  @column({ columnName: 'birthday_day' })
  public birthdayDay: number | null

  @column({ columnName: 'type' })
  public type: 'child' | 'parent'

  @column({ columnName: 'docs' })
  public docs: any | null

  @column({ columnName: 'account_exists' })
  public accountExists: boolean

  @column({ columnName: 'is_active' })
  public isActive: boolean

  @column({ columnName: 'status' })
  public status: 'pending' | 'validated' | 'blocked' | 'archived'

  @column({ columnName: 'validated_by' })
  public validatedBy: string | null

  @column.dateTime({ columnName: 'validated_at' })
  public validatedAt: DateTime | null

  @column.dateTime({ columnName: 'blocked_at' })
  public blockedAt: DateTime | null

  @column.dateTime({ columnName: 'archived_at' })
  public archivedAt: DateTime | null

  @column.dateTime({ autoCreate: true, columnName: 'created_at' })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true, columnName: 'updated_at' })
  public updatedAt: DateTime

  // Relations
  @belongsTo(() => InsuranceCompany, { foreignKey: 'insuranceCompanyId' })
  public insurance_company: BelongsTo<typeof InsuranceCompany>

  @belongsTo(() => InsuranceCompanyAgency, { foreignKey: 'insuranceCompanyAgencyId' })
  public agency: BelongsTo<typeof InsuranceCompanyAgency>

  @belongsTo(() => Patient, { foreignKey: 'patientId' })
  public patient: BelongsTo<typeof Patient>

  @belongsTo(() => PatientInsuranceCompany, { foreignKey: 'parentId' })
  public parent: BelongsTo<typeof PatientInsuranceCompany>
}
