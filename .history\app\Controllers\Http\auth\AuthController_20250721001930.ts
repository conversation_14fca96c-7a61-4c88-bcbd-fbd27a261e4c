import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

import { ApiResponse, UserStatus } from "App/Controllers/interfaces";
import HelperController from "../helpers/HelperController";
import { rules, schema } from '@ioc:Adonis/Core/Validator';
import User from "App/Models/User";
import Hash from '@ioc:Adonis/Core/Hash';
import { DateTime } from 'luxon';
import Team from 'App/Models/Team';
import Patient from 'App/Models/Patient';
import Database from '@ioc:Adonis/Lucid/Database';
import cuid from 'cuid';
import Wallet from 'App/Models/Wallet';

export default class AuthController extends HelperController {

  public async login({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
    }
    try {
      const payload = await request.validate({
        schema: schema.create({
          username: schema.string(),
          password: schema.string(),
        }),
        messages: {
          'username.required': "Le nom utilisateur ou le numero de téléphone est requis",
          'password.required': "Veuillez saisir votre mot de passe correct"
        }
      });

      const user = await User.query().where('email', payload.username).orWhere('phone', payload.username).first();
      if (!user) {
        apiResponse.message = "Identifiant ou mot de passe incorrect";
        return response.status(401).json(apiResponse);
      }
      const Manager = await this.getManagerByUser(user.id);
      if (Manager.role != 'hr_manager' && Manager.role != 'admin') {
        apiResponse.message = "Vous n'êtes pas autoriser à acceder à cette ressource";
        return response.status(401).json(apiResponse);
      }
      let checkPwd = await Hash.verify(user.password, payload.password);
      if (!checkPwd) {
        apiResponse.message = "Identifiant ou mot de passe incorrect";
        return response.status(401).json(apiResponse);
      }

      user.online = 1;
      await user.save();
      const new_connection = await user.related('userHistories').create({
        userId: user.id,
        connectedAt: DateTime.now(),
        ipAddress: request.ip(),
        userAgent: request.header('user-agent') || '',
        deviceType: request.header('user-agent'),
        type: "login",
      });
      const token = await auth.use('api').generate(user, {
        expiresIn: '1day',
        name: user.username,
      });
      apiResponse.success = true;
      apiResponse.message = "Connexion reussie";
      apiResponse.result = {
        token: token,
        user: user,
        team: Manager.team,
        manager: Manager,
        connection: new_connection
      }
      return response.status(200).json(apiResponse);

    } catch (error) {
      console.log("error login", error);

      apiResponse.message = "Une erreur est survenue lors de la connexion, veuillez reessayer plus tard";
      apiResponse.except = error.message;
      return response.status(401).json(apiResponse);
    }
  }

  public async registerTeam({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
    }
    let status = 201;
    try {
      const payload = await request.validate({
        schema: schema.create({
          name: schema.string(),
          legalName: schema.string.optional(),
          registrationNumber: schema.string.optional(),
          type: schema.enum(['company', 'association', 'collective', 'other']),
          industry: schema.string(),
          employeeCount: schema.number.optional(),
          contactEmail: schema.string(),
          contactPhone: schema.string(),
          address: schema.string.optional(),
          postalCode: schema.string.optional(),
          cityId: schema.number.optional(),
          countryId: schema.number.optional(),
          responsable: schema.object().members({
            first_name: schema.string(),
            last_name: schema.string(),
            email: schema.string(),
            phone: schema.string(),
            gender: schema.enum(['M', 'F']),
            birthday_year: schema.number(),
            birthday_month: schema.number(),
            birthday_day: schema.number(),
            role: schema.enum(['admin', 'hr_manager']),
            password: schema.string([
              rules.minLength(8),
              rules.confirmed('password_confirmation'),
              rules.regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/)
            ]),
          }),
          insurance: schema.object().members({
            insurance_company_id: schema.number(),
            agency_id: schema.number.optional(),
          })
        }),
        messages: {
          'name.required': "Veuillez saisir le nom de l'entreprise",
          'type.required': "Veuillez saisir le type de l'entreprise",
          'industry.required': "Veuillez saisir l'industrie de l'entreprise",
          'contactEmail.required': "Veuillez saisir l'email de contact de l'entreprise",
          'contactPhone.required': "Veuillez saisir le numéro de téléphone de contact de l'entreprise",
          'responsable.first_name.required': "Veuillez saisir le prénom du responsable",
          'responsable.last_name.required': "Veuillez saisir le nom du responsable",
          'responsable.email.required': "Veuillez saisir l'email du responsable",
          'responsable.phone.required': "Veuillez saisir le numéro de téléphone du responsable",
          'responsable.gender.required': "Veuillez saisir le genre du responsable",
          'responsable.birthday_year.required': "Veuillez saisir l'année de naissance du responsable",
          'responsable.birthday_month.required': "Veuillez saisir le mois de naissance du responsable",
          'responsable.birthday_day.required': "Veuillez saisir le jour de naissance du responsable",
          'responsable.role.required': "Veuillez saisir le role du responsable",
          'responsable.password.required': "Veuillez saisir le mot de passe du responsable",
          'responsable.password_confirmation.required': "Veuillez confirmer le mot de passe du responsable",
          'responsable.password.minLength': "Le mot de passe doit contenir au moins 8 caractères",
          'responsable.password.confirmed': "Les mots de passe ne correspondent pas",
          'responsable.password.regex': "Le mot de passe doit contenir au moins une lettre majuscule, une lettre minuscule, un chiffre et un caractère spécial",
        }
      });
      const { responsable, insurance, ...team } = payload;

      const checkExist = await Team.query().where('name', team.name).orWhere('legal_name', String(team.legalName)).first();
      if (checkExist) {
        apiResponse.message = "Ce nom ou ce numero de registre est déjà utilisé";
        return response.status(400).json(apiResponse);
      }
      const checkExistResponsable = await User.query().where('email', responsable.email).orWhere('phone', responsable.phone).first();
      if (checkExistResponsable) {
        apiResponse.message = "Un compte existe déjà avec cet email ou ce numéro de téléphone. Veuillez utiliser d'autres coordonnées ou vous connecter si vous avez déjà un compte.";
        return response.status(400).json(apiResponse);
      }

      const trx = await Database.transaction();
      try {
        let newUser: User = {} as User;
        let newPatient: Patient = {} as Patient;
        let publicId = cuid();
        let teamCode = team.name.substring(0, 3).toUpperCase() + Math.random().toString(36).substring(2, 6).toUpperCase();
        const newTeam = await Team.create({
          publicId: publicId,
          teamCode: teamCode,
          name: team.name,
          legalName: String(team.legalName),
          registrationNumber: String(team.registrationNumber),
          type: team.type as 'company' | 'association' | 'collective' | 'other',
          industry: String(team.industry),
          employeeCount: Number(team.employeeCount),
          contactEmail: String(team.contactEmail),
          contactPhone: String(team.contactPhone),
          address: String(team.address),
          postalCode: String(team.postalCode),
          cityId: team.cityId ? Number(team.cityId) : null,
          countryId: team.countryId ? Number(team.countryId) : null,
        }, { client: trx });

        if (!newTeam) {
          await trx.rollback();
          apiResponse.message = "Echec de création du groupe, une erreur serveur s'est produite";
          status = 500;
          return response.status(status).json(apiResponse);
        }

        if (responsable) {
          const checkUser = await User.query().where('email', responsable.email).orWhere('phone', responsable.phone).where('role_id', 6).first();

          if (!checkUser) {
            let codeP = await this.generateCodeParrainage(8);
            const parrainage = {
              create_account: 0,
              active_qrcode: 0,
              adhesion_fees: 0,
              plan: 1,
              activeMoney: false
            }
            let username = responsable.first_name + responsable.last_name;
            newUser = await User.create({
              username: username,
              email: responsable.email,
              phone: responsable.phone,
              password: responsable.password,
              countryId: team.countryId,
              languageId: 1,
              roleId: 6,
              status: UserStatus.Actived,
              activatedAt: DateTime.now(),
              codeParrainage: codeP,
              parrainage: JSON.stringify(parrainage),
            }, { client: trx });

            if (!newUser) {
              await trx.rollback();
              apiResponse.message = "Echec de création du groupe, une erreur serveur s'est produite";
              status = 500;
              return response.status(status).json(apiResponse);
            }
            let codePatient = await this.generateUUID();
            newPatient = await Patient.create({
              first_name: responsable.first_name,
              last_name: responsable.last_name,
              phone: responsable.phone,
              email: responsable.email,
              gender: responsable.gender,
              birthday_year: responsable.birthday_year,
              birthday_month: responsable.birthday_month,
              birthday_day: responsable.birthday_day,
              user_id: newUser.id,
              status: 'activated',
              code: codePatient,
              country_id: team.countryId ? team.countryId : null,
              city_id: team.cityId ? team.cityId : null,
            }, { client: trx });
            if (!newPatient) {
              await trx.rollback();
              apiResponse.message = "Echec de création du compte du responsable , une erreur serveur s'est produite";
              status = 500;
              return response.status(status).json(apiResponse);
            }
          }

          const newTeamMember = await newTeam.related('members').create({
            patientId: newPatient.id,
            publicId: cuid(),
            role: responsable.role as 'admin' | 'hr_manager' | 'member',
            createBySelf: true,
            joinedAt: DateTime.now(),
          }, { client: trx });
          if (!newTeamMember) {
            await trx.rollback();
            apiResponse.except = newTeamMember;
            apiResponse.message = "Echec de création du groupe, une erreur serveur s'est produite";
            status = 500;
            return response.status(status).json(apiResponse);
          }
        }

        const newWallet = await Wallet.create({
          code: await this.generateWalletCode(),
          balance: 0,
          ownerType: 'team',
          ownerId: newTeam.id,
          libelle: newTeam.name + " Wallet",
          typeWalletId: 2,
        }, { client: trx });

        if (!newWallet) {
          await trx.rollback();
          apiResponse.except = newWallet;
          apiResponse.message = "Echec de création du groupe, une erreur serveur s'est produite";
          status = 500;
          return response.status(status).json(apiResponse);
        }

        if (insurance) {
          let publicId = cuid();
          const newInsurance = await newTeam.related('team_insurance_companies').create({
            insuranceCompanyId: insurance.insurance_company_id,
            insuranceCompanyAgencyId: insurance.agency_id,
            isActive: false,
            publicId: publicId,
            status: 'pending',
          }, { client: trx });
          if (!newInsurance) {
            await trx.rollback();
            apiResponse.except = newInsurance;
            apiResponse.message = "Echec de création du groupe, une erreur serveur s'est produite";
            status = 500;
            return response.status(status).json(apiResponse);
          }
        }

        await trx.commit();
        apiResponse.success = true;
        apiResponse.message = "Groupe créé avec succès";
        apiResponse.result = {
          team: newTeam,
          responsable: newPatient,
        };
        return response.status(status).json(apiResponse);
      } catch (error) {
        await trx.rollback();
        console.log("error in add new team", error);

        apiResponse.message = "Echec de création du groupe, une erreur serveur s'est produite";
        apiResponse.except = error.message;
        status = 500;
        return response.status(status).json(apiResponse);
      }

    } catch (error) {
      console.log("error registerTeam", error.message)
      ;

      apiResponse.message = "Une erreur est survenue lors de l'inscription, veuillez reessayer plus tard";
      apiResponse.except = error.message;
      status = 500;
      return response.status(status).json(apiResponse);
    }
  }

  public async logout({ response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
    }
    try {
      const user = auth.user;
      if (!user) {
        apiResponse.message = "Vous n'êtes pas connecté";
        return response.status(401).json(apiResponse);
      }
      await auth.use('api').revoke();
      user.online = 0;
      await user.save();
      apiResponse.success = true;
      apiResponse.message = "Déconnexion reussie";
      return response.status(200).json(apiResponse);
    } catch (error) {
      apiResponse.message = "Une erreur est survenue lors de la déconnexion, veuillez reessayer plus tard";
      apiResponse.except = error.message;
      return response.status(401).json(apiResponse);
    }
  }


}
