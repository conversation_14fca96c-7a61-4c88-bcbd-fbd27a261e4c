import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'snapshot_products'

  public async up () {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.integer('package_snapshot_id').unsigned().notNullable().references('id').inTable('package_snapshots').onDelete('CASCADE')
      table.integer('product_id')
      table.decimal('public_price', 18, 2)
      table.integer('quantity')
      table.boolean('is_active').defaultTo(true)

      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })

      table.index(['package_snapshot_id', 'product_id'], 'idx_snapshot_product')

      table.comment("Snapshot products")
    })
  }

  public async down () {
    this.schema.dropTable(this.tableName)
  }
}
