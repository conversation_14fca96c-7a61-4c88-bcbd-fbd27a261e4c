import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column, HasMany, hasMany } from '@ioc:Adonis/Lucid/Orm'
import QuotationRequest from './QuotationRequest'
import { BelongsTo } from '@ioc:Adonis/Lucid/Orm'
import Pharmacy from './Pharmacy'
import Laboratory from './Laboratory'
import User from './User'
import QuotationProposalItem from './QuotationProposalItem'

export default class QuotationProposal extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'reference' })
  public reference: string

  @column({ columnName: 'quotation_request_id' })
  public quotationRequestId: number // La demande de devis

  @column({ columnName: 'pharmacy_id' })
  public pharmacyId?: number

  @column({ columnName: 'laboratory_id' })
  public laboratoryId?: number

  @column({ columnName: 'proposal_user_id' })
  public proposalUserId: number

  @column({ columnName: 'total_price' })
  public totalPrice: number

  @column({ columnName: 'total_assured_price' })
  public totalAssuredPrice?: number

  @column({ columnName: 'total_items' })
  public totalItems?: number

  @column({ columnName: 'status' })
  public status: 'pending' | 'approved' | 'rejected' | 'expired' = 'pending' // Statut de la proposition

  @column({ columnName: 'approved_at' })
  public approvedAt?: DateTime

  @column({ columnName: 'rejected_at' })
  public rejectedAt?: DateTime

  @column({ columnName: 'expired_at' })
  public expiredAt?: DateTime

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(()=>QuotationRequest,{
    foreignKey: 'quotationRequestId',
    localKey: 'id'
  })
  public quotationRequest: BelongsTo<typeof QuotationRequest>

  @belongsTo(()=>Pharmacy,{
    foreignKey: 'pharmacyId',
    localKey: 'id'
  })
  public pharmacy: BelongsTo<typeof Pharmacy>

  @belongsTo(()=>Laboratory,{
    foreignKey: 'laboratoryId',
    localKey: 'id'
  })
  public laboratory: BelongsTo<typeof Laboratory>

  @belongsTo(()=>User,{
    foreignKey: 'proposalUserId',
    localKey: 'id'
  })
  public proposalUser: BelongsTo<typeof User>

  @hasMany(()=>QuotationProposalItem,{
    foreignKey: 'quotationProposalId',
    localKey: 'id'
  })
  public items: HasMany<typeof QuotationProposalItem>
}
