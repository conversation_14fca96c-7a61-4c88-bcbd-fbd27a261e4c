import { DateTime } from 'luxon'
import { BaseModel, column, HasMany, hasMany } from '@ioc:Adonis/Lucid/Orm'
import SnapshotProduct from './SnapshotProduct'
import SnapshotAnalyze from './SnapshotAnalyze'

export default class PackageSnapshot extends BaseModel {

  @column({ isPrimary: true, columnName: 'id' })
  public id: number

  @column({ columnName: 'package_id' })
  public packageId: number

  @column({ columnName: 'insurance_year_id' })
  public insuranceYearId: number | null

  @column({ columnName: 'insurance_company_id' })
  public insuranceCompanyId: number | null

  @column({ columnName: 'name' })
  public name: string | null

  @column({ columnName: 'type' })
  public type: 'individual' | 'team'

  @column({ columnName: 'price' })
  public price: number

  @column({ columnName: 'taux' })
  public taux: number

  @column({ columnName: 'plafond' })
  public plafond: number

  @column({ columnName: 'fees_config' })
  public feesConfig: any | null

  @column({ columnName: 'plafond_config' })
  public plafondConfig: any | null

  @column({ columnName: 'tranches_config' })
  public tranchesConfig: any | null

  @column({ columnName: 'version' })
  public version: string | null

  @column({ columnName: 'checksum' })
  public checksum: string

  @column.dateTime({ autoCreate: true, columnName: 'created_at' })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true, columnName: 'updated_at' })
  public updatedAt: DateTime

  @hasMany(() => SnapshotProduct, {
    localKey: 'id',
    foreignKey: 'packageSnapshotId'
  })
  public snapshot_products: HasMany<typeof SnapshotProduct>

  @hasMany(() => SnapshotAnalyze, {
    localKey: 'id',
    foreignKey: 'packageSnapshotId'
  })
  public snapshot_analyzes: HasMany<typeof SnapshotAnalyze>
}
