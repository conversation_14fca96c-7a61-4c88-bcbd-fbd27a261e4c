import { DateTime } from 'luxon'
import { BaseModel, belongsTo, BelongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import Package from './Package'

export default class PackageAnalyze extends BaseModel {

  public static connection = 'primary';

  @column({ isPrimary: true })
  public id: number

  @column({columnName: 'package_id'})
  public packageId: number

  @column({columnName: 'analyze_id'})
  public analyzeId: number

  @column({columnName: 'is_active'})
  public isActive: boolean

  @column({columnName: 'public_price'})
  public publicPrice: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => Package,{
    foreignKey: 'packageId',
    localKey: 'id',
  })
  public package: BelongsTo<typeof Package>

}
