import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column, HasMany, hasMany, HasOne, hasOne } from '@ioc:Adonis/Lucid/Orm'
import Team from './Team'
import TeamGroup from './TeamGroup'
import TeamInsuranceCompany from './TeamInsuranceCompany'
import InsuranceCompany from './InsuranceCompany'
import NegotiationVersion from './NegotiationVersion'
import TeamMember from './TeamMember'
import InsuranceManager from './InsuranceManager'

export default class Negotiation extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'public_id' })
  public publicId: string

  @column()
  public reference: string

  @column({ columnName: 'team_id' })
  public teamId: number

  @column({ columnName: 'team_group_id' })
  public teamGroupId: number

  @column({columnName: 'team_insurance_company_id'})
  public teamInsuranceCompanyId: number

  @column({ columnName: 'insurance_company_id' })
  public insuranceCompanyId: number

  @column({ columnName: 'created_by' })
  public createdBy: number

  @column({ columnName: 'approved_by' })
  public approvedBy: number | null

  @column({ columnName: 'accepted_by' })
  public acceptedBy: number | null

  @column({ columnName: 'rejected_by' })
  public rejectedBy: number | null

  @column()
  public status: 'draft' | 'pending' | 'approved' | 'rejected' | 'accepted' | 'expired' = 'draft'

  @column.date({ columnName: 'approved_at' })
  public approvedAt: DateTime | null

  @column.date({ columnName: 'accepted_at' })
  public acceptedAt: DateTime | null

  @column.date({ columnName: 'rejected_at' })
  public rejectedAt: DateTime | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => Team, {
    foreignKey: 'teamId',
    localKey: 'id',
  })
  public team: BelongsTo<typeof Team>

  @belongsTo(() => TeamGroup, {
    foreignKey: 'teamGroupId',
    localKey: 'id',
  })
  public team_group: BelongsTo<typeof TeamGroup>

  @belongsTo(() => TeamInsuranceCompany, {
    foreignKey: 'teamInsuranceCompanyId',
    localKey: 'id',
  })
  public team_insurance_company: BelongsTo<typeof TeamInsuranceCompany>

  @belongsTo(() => InsuranceCompany, {
    foreignKey: 'insuranceCompanyId',
    localKey: 'id',
  })
  public insurance_company: BelongsTo<typeof InsuranceCompany>

  @hasMany(() => NegotiationVersion, {
    foreignKey: 'negotiationId',
    localKey: 'id',
  })
  public versions: HasMany<typeof NegotiationVersion>

  @hasOne(() => NegotiationVersion, {
    foreignKey: 'negotiationId',
    localKey: 'id',
    onQuery(query) {
      query.orderBy('created_at', 'desc').first();
    },
  })
  public current_version: HasOne<typeof NegotiationVersion>

  @hasOne(() => NegotiationVersion, {
    foreignKey: 'negotiationId',
    localKey: 'id',
    onQuery(query) {
      query.where('is_validated', true).orderBy('created_at', 'desc').first();
    },
  })
  public validated_version: HasOne<typeof NegotiationVersion>

  @belongsTo(() => TeamMember, {
    foreignKey: 'createdBy',
    localKey: 'id',
  })
  public creator: BelongsTo<typeof TeamMember>

  @belongsTo(() => InsuranceManager, {
    foreignKey: 'approvedBy',
    localKey: 'id',
  })
  public approver: BelongsTo<typeof InsuranceManager>

  @belongsTo(() => TeamMember, {
    foreignKey: 'acceptedBy',
    localKey: 'id',
  })
  public accepter: BelongsTo<typeof TeamMember>

  @belongsTo(() => InsuranceManager, {
    foreignKey: 'rejectedBy',
    localKey: 'id',
  })
  public rejecter: BelongsTo<typeof InsuranceManager>
}
