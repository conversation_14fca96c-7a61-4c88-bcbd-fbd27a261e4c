import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class AnalyzeAskProposition extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column({ columnName: 'analyze_ask_order_id' })
  public analyzeAskOrderId: number

  @column({ columnName: 'simulation_id' })
  public simulationId: number

  @column({ columnName: 'laboratory_id' })
  public laboratoryId: number

  @column({ columnName: 'pro_id' })
  public proId: number

  @column()
  public status: string

  @column.dateTime()
  public acceptedAt: DateTime

  @column.dateTime()
  public rejectedAt: DateTime

  @column.dateTime()
  public expiredAt: DateTime

  @column.dateTime()
  public paymentRequestAt: DateTime

  @column.dateTime()
  public paidAt: DateTime
}
