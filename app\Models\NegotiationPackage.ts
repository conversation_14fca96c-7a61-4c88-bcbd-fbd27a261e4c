import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class NegotiationPackage extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'negotiation_version_id' })
  public negotiationVersionId: number

  @column({ columnName: 'annual_prime' })
  public annualPrime: number | null

  @column({
    columnName: 'family_settings',
    serialize: (value) => JSON.stringify(value),
    prepare: (value) => JSON.stringify(value),
    consume: (value) => typeof value === 'string' ? JSON.parse(value) : value,
  })
  public familySettings: {
    max_dependents: number
    partner_coverage: boolean
    children_age_limit: number
    principal_plafond: number
    partner_plafond: number
    child_plafond: number
  } | null

  @column({
    columnName: 'guarantees',
    serialize: (value) => JSON.stringify(value),
    prepare: (value) => JSON.stringify(value),
    consume: (value) => typeof value === 'string' ? JSON.parse(value) : value,
  })
  public guarantees: {
    type: number
    coverage_rate: number
    annual_limit: number
    comment?: string
  }[] | null

  @column({ columnName: 'coverage_period' })
  public coveragePeriod: number | null

  @column.date({ columnName: 'coverage_start' })
  public coverageStart: DateTime | null

  @column.date({ columnName: 'coverage_end' })
  public coverageEnd: DateTime | null

  @column()
  public metadata: any | null

  @column.dateTime({ columnName: 'agreed_at' })
  public agreedAt: DateTime | null

  @column.dateTime({ columnName: 'rejected_at' })
  public rejectedAt: DateTime | null

  @column({ columnName: 'agreed_by' })
  public agreedBy: number | null

  @column({ columnName: 'rejected_by' })
  public rejectedBy: number | null

  @column()
  public comment: string | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
