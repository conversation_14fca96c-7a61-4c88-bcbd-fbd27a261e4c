import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column, hasMany, <PERSON><PERSON>any } from '@ioc:Adonis/Lucid/Orm'
import Patient from './Patient'
import { BelongsTo } from '@ioc:Adonis/Lucid/Orm'
import Prescription from './Prescription'
import AnalyzeAsk from './AnalyzeAsk'
import QuotationProposal from './QuotationProposal'
import Order from './Order'

export default class QuotationRequest extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column()
  public reference: string

  @column({ columnName: 'requested_by' })
  public requestedBy: number | null

  @column({columnName: 'patient_id'})
  public patientId: number

  @column({ columnName: 'prescription_id' })
  public prescriptionId: number | null

  @column({ columnName: 'analyze_ask_id' })
  public analyzeAskId: number | null

  @column({columnName: 'type_request'})
  public typeRequest: 'prescription' | 'analyze'

  @column({ columnName: 'type_order' })
  public typeOrder: string

  @column({ columnName: 'priority' })
  public priority: 'urgent' | 'normal' | 'low'

  @column({ columnName: 'total_items' })
  public totalItems: number | null

  @column({ columnName: 'comment' })
  public comment: string | null

  @column({ columnName: 'is_active' })
  public isActive: number

  @column({ columnName: 'status' })
  public status: 'pending' | 'proposal_received' | 'in_progress' | 'completed' | 'cancelled' | 'paid' | 'partially_paid' | 'expired'

  @column({columnName: 'location'})
  public location: any | null

  @column({ columnName: 'expired_at' })
  public expiredAt: string | null

  @column({ columnName: 'completed_at' })
  public completedAt: string | null

  @column({ columnName: 'cancelled_at' })
  public cancelledAt: string | null

  @column({ columnName: 'paid_at' })
  public paidAt: string | null

  @belongsTo(()=>Patient,{
    foreignKey: 'patientId',
    localKey: 'id'
  })
  public patient: BelongsTo<typeof Patient>

  @belongsTo(()=>Prescription,{
    foreignKey: 'prescriptionId',
    localKey: 'id'
  })
  public prescription: BelongsTo<typeof Prescription>

  @belongsTo(()=>AnalyzeAsk,{
    foreignKey: 'analyzeAskId',
    localKey: 'id'
  })
  public analyzeAsk: BelongsTo<typeof AnalyzeAsk>

  @hasMany(() => QuotationProposal,{
    foreignKey: 'quotationRequestId',
    localKey: 'id'
  })
  public proposals: HasMany<typeof QuotationProposal>

  @hasMany(() => Order,{
    foreignKey: 'quotationRequestId',
    localKey: 'id'
  })
  public orders: HasMany<typeof Order>

}
