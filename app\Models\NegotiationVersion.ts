import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, HasOne, belongsTo, column, hasOne } from '@ioc:Adonis/Lucid/Orm'
import NegotiationPackage from './NegotiationPackage'
import Negotiation from './Negotiation'
import TeamMember from './TeamMember'
import InsuranceManager from './InsuranceManager'

export default class NegotiationVersion extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'public_id' })
  public publicId: string | null

  @column({ columnName: 'negotiation_id' })
  public negotiationId: number

  @column()
  public version: string

  @column({ columnName: 'actor_type' })
  public actorType: 'team_member' | 'insurance_manager'

  @column({ columnName: 'actor_id' })
  public actorId: number

  @column()
  public resume: string | null

  @column()
  public metadata: any | null

  @column()
  public status: 'pending' | 'approved' | 'rejected' = 'pending'

  @column({ columnName: 'is_validated' })
  public isValidated: boolean = false

  @column.dateTime({ columnName: 'approved_at' })
  public approvedAt: DateTime | null

  @column.dateTime({ columnName: 'rejected_at' })
  public rejectedAt: DateTime | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @hasOne(() => NegotiationPackage, {
    foreignKey: 'negotiationVersionId',
    localKey: 'id',
  })
  public negotiationPackage: HasOne<typeof NegotiationPackage>

  @belongsTo(() => Negotiation, {
    foreignKey: 'negotiationId',
    localKey: 'id',
  })
  public negotiation: BelongsTo<typeof Negotiation>

  @belongsTo(() => TeamMember, {
    foreignKey: 'actorId',
    localKey: 'id',
    onQuery(query) {
      query.where('id', this.actorId).where('actor_type', 'team_member')
    },
  })
  public team_member: BelongsTo<typeof TeamMember>

  @belongsTo(() => InsuranceManager, {
    foreignKey: 'actorId',
    localKey: 'id',
    onQuery(query) {
      query.where('id', this.actorId).where('actor_type', 'insurance_manager')
    },
  })
  public manager: BelongsTo<typeof InsuranceManager>
}
