// ... existing code ...
import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import Country from './Country'
import City from './City'
import Quarter from './Quarter'

export default class Pharmacy extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column({ columnName: 'name' })
  public name: string

  @column({ columnName: 'type' })
  public type: string

  @column({ columnName: 'phone' })
  public phone: string

  @column({ columnName: 'email' })
  public email: string

  @column({ columnName: 'website' })
  public website: string

  @column({ columnName: 'health_institute_id' })
  public healthInstituteId: number | null

  @column({ columnName: 'country_id' })
  public countryId: number

  @column({ columnName: 'city_id' })
  public cityId: number

  @column({ columnName: 'quarter_id' })
  public quarterId: number

  @column({ columnName: 'address' })
  public address: string

  @column({ columnName: 'location' })
  public location: any | null

  @column({ columnName: 'description' })
  public description: string

  @column({ columnName: 'opening_hours' })
  public openingHours: any

  @column({ columnName: 'responsable' })
  public responsable: any

  @column({ columnName: 'personals' })
  public personals: any

  @column({ columnName: 'status' })
  public status: string

  @column({ columnName: 'is_partner' })
  public isPartner: boolean

  @column({columnName: 'channel'})
  public channel: string | null

  @belongsTo(() => Country, {
    foreignKey: 'countryId',
    localKey: 'id'
  })
  public country: BelongsTo<typeof Country>

  @belongsTo(() => City, {
    foreignKey: 'cityId',
    localKey: 'id'
  })
  public city: BelongsTo<typeof City>

  @belongsTo(() => Quarter, {
    foreignKey: 'quarterId',
    localKey: 'id'
  })
  public quarter: BelongsTo<typeof Quarter>
}
