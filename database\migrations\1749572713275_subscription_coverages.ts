import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'subscription_coverages'

  public async up () {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.integer('subscription_id');
      table.integer('package_snapshot_id').unsigned().notNullable().references('id').inTable('package_snapshots').onDelete('CASCADE')
      table.date('start_date').notNullable()
      table.date('end_date').notNullable()

      // Statut de la couverture
      table.enum('status', ['active', 'expired', 'terminated']).defaultTo('active')
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })

      table.index(['package_snapshot_id', 'subscription_id'], 'idx_subscription_coverage')
    })
  }

  public async down () {
    this.schema.dropTable(this.tableName)
  }
}
