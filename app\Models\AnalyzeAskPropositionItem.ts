import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class AnalyzeAskPropositionItem extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column({ columnName: 'analyze_ask_item_id' })
  public analyzeAskItemId: number

  @column({ columnName: 'analyze_id' })
  public analyzeId: number

  @column()
  public name: string

  @column()
  public image: string

  @column()
  public price: number

  @column({columnName: 'assurend_price'})
  public assuredPrice: number

  @column()
  public choosed: boolean

  @column({columnName: 'is_assured'})
  public isAssured: boolean

  @column({columnName: 'is_paid'})
  public isPaid: boolean

  @column({columnName: 'is_refund'})
  public isRefund: boolean
}
