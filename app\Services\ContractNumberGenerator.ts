// app/Services/ContractNumberGenerator.ts
import { DateTime } from 'luxon'
import InsuranceCompany from 'App/Models/InsuranceCompany'

export default class ContractNumberGenerator {
  public static async generate(
    company: InsuranceCompany,
    teamCode: string,
  ): Promise<string> {
    const settings = company.contractNumberSettings
    const template = company.contractNumberTemplate || '{COMPANY}-{TEAM}-{DATE}-{SEQ}'
    const separator = settings.defaultSeparator

    // Validation du séparateur
    if (!settings.separators.includes(separator)) {
      throw new Error('Separator not allowed')
    }

    // Incrémenter et sauvegarder la séquence
    const nextSequence = company.contractNumberSequence
    company.contractNumberSequence += 1
    await company.save()

    // Formatage des segments
    const segments = {
      COMPANY: this.formatSegment(
        company.code,
        settings.segmentLengths.COMPANY,
        settings.padding.COMPANY
      ),
      TEAM: this.formatSegment(
        teamCode,
        settings.segmentLengths.TEAM,
        settings.padding.TEAM
      ),
      DATE: DateTime.now().toFormat('yyyyLL'), // AAAAMM
      SEQ: this.formatSegment(
        nextSequence.toString(),
        settings.segmentLengths.SEQ,
        settings.padding.SEQ
      )
    }

    // Construction du numéro
    let contractNumber = template
      .replace('{COMPANY}', segments.COMPANY)
      .replace('{TEAM}', segments.TEAM)
      .replace('{DATE}', segments.DATE)
      .replace('{SEQ}', segments.SEQ)
      .replace(/-/g, separator) // Remplace le séparateur par défaut

    // Ajustement à la longueur fixe
    if (contractNumber.length > settings.fixedLength) {
      contractNumber = contractNumber.substring(0, settings.fixedLength)
    } else {
      contractNumber = contractNumber.padEnd(settings.fixedLength, 'X')
    }

    return contractNumber
  }

  private static formatSegment(
    value: string,
    length: number,
    padding: 'left' | 'right' | 'none'
  ): string {
    switch (padding) {
      case 'left':
        return value.padStart(length, '0')
      case 'right':
        return value.padEnd(length, '0')
      default: // 'none'
        return value.substring(0, length)
    }
  }
}