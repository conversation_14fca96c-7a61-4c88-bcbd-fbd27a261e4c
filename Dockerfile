ARG NODE_IMAGE=node:20.17.0

FROM $NODE_IMAGE AS base

WORKDIR /home/<USER>/app

# Mettre à jour npm et configurer le registre (optionnel)
RUN npm install -g npm@11.4.2 && \
  npm config set fetch-retries 5 && \
  npm config set fetch-retry-mintimeout 20000

# Copier les fichiers de dépendances
COPY package.json package-lock.json ./

# Installer les dépendances avec des optimisations réseau
RUN npm ci --quiet --no-audit --no-fund --network-timeout 100000

# Copier le reste de l'application
COPY . .

# Variables d'environnement
ARG HOST
ARG PORT
ENV HOST=$HOST
ENV PORT=$PORT

# Étape de développement
FROM base AS dev
ENV CHOKIDAR_USEPOLLING=true
ENV NODE_ENV=development
CMD ["node", "ace", "serve", "--watch", "--node-args=--inspect=0.0.0.0"]
