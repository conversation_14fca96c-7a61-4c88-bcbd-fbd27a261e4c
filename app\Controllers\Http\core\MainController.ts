import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

import HelperController from "../helpers/HelperController";
import { ApiResponse } from 'App/Controllers/interfaces';
import { schema } from '@ioc:Adonis/Core/Validator';
import Database from '@ioc:Adonis/Lucid/Database';
import GuaranteeType from 'App/Models/GuaranteeType';
import Package from 'App/Models/Package';

export default class MainController extends HelperController {

  public async index({ response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
    }
    try {
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté. Veuillez vous authentifier pour accéder à cette ressource.";
        return response.status(401).json(apiResponse);
      }
      const team = await this.getTeamByManager(authUser.id);

      const groups = await team.related('groups').query().withCount('members').orderBy('created_at', 'desc');
      const formattedGroups = groups.map(group => ({
        ...group.toJSON(),
        members_count: group.$extras.members_count
      }));
      const members_count = groups.reduce((acc, group) => acc + group.$extras.members_count, 0);

      const current_insurance_company = await team.related('team_insurance_companies')
        .query()
        .where((query) => {
          query.where('is_active', true).orWhere('is_active', false);
        })
        .where((query) => {
          query.where('status', 'pending').orWhere('status', 'validated');
        })
        .preload('insurance_company')
        .preload('insurance_company_agency')
        .first();

      apiResponse.result = {
        team: team,
        members_count,
        current_insurance_company,
        groups: formattedGroups,
      };
      apiResponse.success = true;
      return response.status(200).json(apiResponse);
    } catch (error) {
      apiResponse.message = "Une erreur est survenue lors de la récupération des informations de l'utilisateur, veuillez reessayer plus tard";
      apiResponse.except = error.message;
      return response.status(401).json(apiResponse);
    }
  }

  public async getDashboardData({ response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
    }
    try {
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté. Veuillez vous authentifier pour accéder à cette ressource.";
        return response.status(401).json(apiResponse);
      }
      const team = await this.getTeamByManager(authUser.id);

      // Récupération des groupes avec statistiques optimisées
      const groups = await team.related('groups').query()
        .withCount('members')
        .orderBy('created_at', 'desc');

      // Calcul des statistiques globales avec des requêtes d'agrégation
      const currentYear = new Date().getFullYear();

      // Statistiques globales pour toute l'équipe
      const globalStats = await Database
        .from('team_members')
        .join('patients', 'team_members.patient_id', 'patients.id')
        .where('team_members.team_id', team.id)
        .select([
          Database.raw('COUNT(*) as total_members'),
          Database.raw(`AVG(${currentYear} - patients.birthday_year) as average_age`),
          Database.raw("COUNT(CASE WHEN patients.gender = 'M' THEN 1 END) as male_count"),
          Database.raw("COUNT(CASE WHEN patients.gender = 'F' THEN 1 END) as female_count")
        ])
        .first();

      // Calcul des statistiques par groupe
      const groupsWithStats = await Promise.all(
        groups.map(async (group) => {
          const groupStats = await Database
            .from('team_members')
            .join('patients', 'team_members.patient_id', 'patients.id')
            .where('team_members.team_group_id', group.id)
            .select([
              Database.raw(`AVG(${currentYear} - patients.birthday_year) as average_age`),
              Database.raw("COUNT(CASE WHEN patients.gender = 'M' THEN 1 END) as male_count"),
              Database.raw("COUNT(CASE WHEN patients.gender = 'F' THEN 1 END) as female_count")
            ])
            .first();

          return {
            ...group.toJSON(),
            statistics: {
              members_count: group.$extras.members_count,
              average_age: groupStats?.average_age ? Math.round(parseFloat(groupStats.average_age)) : 0,
              gender_distribution: {
                male: parseInt(groupStats?.male_count) || 0,
                female: parseInt(groupStats?.female_count) || 0,
              }
            }
          };
        })
      );

      const members_count = parseInt(globalStats?.total_members) || 0;
      const average_age = globalStats?.average_age ? Math.round(parseFloat(globalStats.average_age)) : 0;
      const gender_distribution = {
        male: parseInt(globalStats?.male_count) || 0,
        female: parseInt(globalStats?.female_count) || 0,
      };

      // Récupération des informations d'assurance
      const current_insurance_company = await team.related('team_insurance_companies')
        .query()
        .where((query) => {
          query.where('is_active', true).orWhere('is_active', false);
        })
        .where((query) => {
          query.where('status', 'pending').orWhere('status', 'validated');
        })
        .preload('insurance_company')
        .preload('insurance_company_agency')
        .first();

      apiResponse.result = {
        team: team,
        statistics: {
          members_count,
          average_age,
          gender_distribution,
          groups_count: groups.length,
          insurance_contracts: current_insurance_company?.$extras.contracts_count || 0,
        },
        current_insurance_company,
        groups: groupsWithStats,
      };
      apiResponse.success = true;
      return response.status(200).json(apiResponse);
    } catch (error) {
      apiResponse.message = "Une erreur est survenue lors de la récupération des informations de l'utilisateur, veuillez reessayer plus tard";
      apiResponse.except = error.message;
      return response.status(500).json(apiResponse);
    }
  }

  public async getMembersAnalytics({ response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
    }
    try {
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté. Veuillez vous authentifier pour accéder à cette ressource.";
        return response.status(401).json(apiResponse);
      }
      const team = await this.getTeamByManager(authUser.id);

      // Utilisation de requêtes Database optimisées pour les statistiques générales de l'équipe
      const currentYear = new Date().getFullYear();

      // Statistiques globales avec une seule requête optimisée
      const globalStats = await Database
        .from('team_members')
        .join('patients', 'team_members.patient_id', 'patients.id')
        .where('team_members.team_id', team.id)
        .whereNull('team_members.deleted_at')
        .select([
          Database.raw('COUNT(*) as total_members'),
          Database.raw('COUNT(CASE WHEN team_members.left_at IS NULL AND team_members.coverage_status = "active" THEN 1 END) as active_members'),
          Database.raw('COUNT(CASE WHEN team_members.coverage_status = "active" AND team_members.left_at IS NULL THEN 1 END) as covered_members'),
          Database.raw('COUNT(CASE WHEN team_members.coverage_status = "pending" AND team_members.left_at IS NULL THEN 1 END) as pending_members'),
          Database.raw('COUNT(CASE WHEN team_members.coverage_status = "suspended" THEN 1 END) as suspended_members'),
          Database.raw('COUNT(CASE WHEN team_members.coverage_status = "terminated" THEN 1 END) as terminated_members'),
          Database.raw('COUNT(CASE WHEN team_members.left_at IS NOT NULL THEN 1 END) as left_members'),
          Database.raw('SUM(team_members.dependent_children) as total_dependents'),
          Database.raw(`AVG(${currentYear} - patients.birthday_year) as average_age`),
          Database.raw('COUNT(CASE WHEN patients.gender = "M" THEN 1 END) as male_count'),
          Database.raw('COUNT(CASE WHEN patients.gender = "F" THEN 1 END) as female_count'),
          Database.raw('COUNT(CASE WHEN team_members.role = "admin" THEN 1 END) as admin_count'),
          Database.raw('COUNT(CASE WHEN team_members.role = "hr_manager" THEN 1 END) as hr_manager_count'),
          Database.raw('COUNT(CASE WHEN team_members.role = "member" THEN 1 END) as member_count')
        ])
        .first();

      // Statistiques par statut marital
      const maritalStats = await Database
        .from('team_members')
        .where('team_id', team.id)
        .whereNull('deleted_at')
        .select([
          Database.raw('COUNT(CASE WHEN marital_status = "single" THEN 1 END) as single_count'),
          Database.raw('COUNT(CASE WHEN marital_status = "married" THEN 1 END) as married_count'),
          Database.raw('COUNT(CASE WHEN marital_status = "divorced" THEN 1 END) as divorced_count'),
          Database.raw('COUNT(CASE WHEN marital_status = "widowed" THEN 1 END) as widowed_count')
        ])
        .first();

      // Statistiques par groupe
      const groupStats = await Database
        .from('team_groups')
        .leftJoin('team_members', 'team_groups.id', 'team_members.team_group_id')
        .where('team_groups.team_id', team.id)
        .where('team_groups.status', 'active')
        .groupBy('team_groups.id', 'team_groups.name')
        .select([
          'team_groups.id',
          'team_groups.name',
          Database.raw('COUNT(team_members.id) as members_count'),
          Database.raw('COUNT(CASE WHEN team_members.coverage_status = "active" AND team_members.left_at IS NULL THEN 1 END) as active_members')
        ]);

      const analytics = {
        overview: {
          total_members: parseInt(globalStats?.total_members) || 0,
          active_members: parseInt(globalStats?.active_members) || 0,
          covered_members: parseInt(globalStats?.covered_members) || 0,
          pending_members: parseInt(globalStats?.pending_members) || 0,
          suspended_members: parseInt(globalStats?.suspended_members) || 0,
          terminated_members: parseInt(globalStats?.terminated_members) || 0,
          left_members: parseInt(globalStats?.left_members) || 0,
          total_dependents: parseInt(globalStats?.total_dependents) || 0,
        },
        demographics: {
          average_age: globalStats?.average_age ? Math.round(parseFloat(globalStats.average_age)) : 0,
          gender_distribution: {
            male: parseInt(globalStats?.male_count) || 0,
            female: parseInt(globalStats?.female_count) || 0,
          },
          marital_status: {
            single: parseInt(maritalStats?.single_count) || 0,
            married: parseInt(maritalStats?.married_count) || 0,
            divorced: parseInt(maritalStats?.divorced_count) || 0,
            widowed: parseInt(maritalStats?.widowed_count) || 0,
          }
        },
        roles: {
          admin: parseInt(globalStats?.admin_count) || 0,
          hr_manager: parseInt(globalStats?.hr_manager_count) || 0,
          member: parseInt(globalStats?.member_count) || 0,
        },
        groups: groupStats.map(group => ({
          id: group.id,
          name: group.name,
          members_count: parseInt(group.members_count) || 0,
          active_members: parseInt(group.active_members) || 0,
        }))
      };

      apiResponse.success = true;
      apiResponse.message = "Statistiques récupérées avec succès";
      apiResponse.result = analytics;
      return response.status(200).json(apiResponse);
    } catch (error) {
      apiResponse.message = "Une erreur est survenue lors de la récupération des statistiques, veuillez réessayer plus tard";
      apiResponse.except = error.message;
      return response.status(500).json(apiResponse);
    }
  }

  public async updateTeamInfo({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
    }
    try {
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté. Veuillez vous authentifier pour accéder à cette ressource.";
        return response.status(401).json(apiResponse);
      }

      const payload = await request.validate({
        schema: schema.create({
          name: schema.string.optional(),
          legalName: schema.string.optional(),
          registrationNumber: schema.string.optional(),
          type: schema.enum.optional(['company', 'association', 'collective', 'other']),
          industry: schema.string.optional(),
          employeeCount: schema.number.optional(),
          contactEmail: schema.string.optional(),
          contactPhone: schema.string.optional(),
          address: schema.string.optional(),
          postalCode: schema.string.optional(),
          cityId: schema.number.optional(),
          countryId: schema.number.optional(),
        }),
        messages: {
          'name.string': "Le nom du groupe doit être une chaîne de caractères",
          'legalName.string': "Le nom légal du groupe doit être une chaîne de caractères",
          'registrationNumber.string': "Le numéro de registre du groupe doit être une chaîne de caractères",
          'type.enum': "Le type du groupe doit être une chaîne de caractères",
          'industry.string': "L'industrie du groupe doit être une chaîne de caractères",
          'employeeCount.number': "Le nombre d'employés du groupe doit être un nombre",
          'contactEmail.string': "L'email du responsable du groupe doit être une chaîne de caractères",
        }
      });

      const team = await this.getTeamByManager(authUser.id);

      team.merge({
        name: payload.name || team.name,
        legalName: payload.legalName || team.legalName,
        registrationNumber: payload.registrationNumber || team.registrationNumber,
        type: payload.type ? (payload.type as 'company' | 'association' | 'collective' | 'other') : team.type,
        industry: payload.industry || team.industry,
        employeeCount: payload.employeeCount || team.employeeCount,
        contactEmail: payload.contactEmail || team.contactEmail,
        contactPhone: payload.contactPhone || team.contactPhone,
        address: payload.address || team.address,
        postalCode: payload.postalCode || team.postalCode,
        cityId: payload.cityId || team.cityId,
        countryId: payload.countryId || team.countryId,
      });
      await team.save();
      apiResponse.success = true;
      apiResponse.message = "Informations du groupe mise à jour avec succès";
      apiResponse.result = team;
      return response.status(200).json(apiResponse);

    } catch (error) {
      apiResponse.message = "Une erreur est survenue lors de la mise à jour des informations du groupe, veuillez reessayer plus tard";
      apiResponse.except = error.message;
      return response.status(401).json(apiResponse);
    }
  }

  public async getCurrentTeamInsuranceCompany({ response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
    }
    try {
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté. Veuillez vous authentifier pour accéder à cette ressource.";
        return response.status(401).json(apiResponse);
      }
      const team = await this.getTeamByManager(authUser.id);
      const insuranceCompany = await team.related('team_insurance_companies')
        .query()
        .where((query) => {
          query.where('is_active', true).orWhere('is_active', false);
        })
        .where((query) => {
          query.where('status', 'pending').orWhere('status', 'validated');
        })
        .first();
      apiResponse.success = true;
      apiResponse.message = "Assurance récupérée avec succès";
      apiResponse.result = insuranceCompany;
      return response.status(200).json(apiResponse);
    } catch (error) {
      apiResponse.message = "Une erreur est survenue lors de la récupération des informations de l'utilisateur, veuillez reessayer plus tard";
      apiResponse.except = error.message;
      return response.status(500).json(apiResponse);
    }
  }

  public async getGuaranteeTypes({ response,auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
    }
    try {
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté. Veuillez vous authentifier pour accéder à cette ressource.";
        return response.status(401).json(apiResponse);
      }
      const team = await this.getTeamByManager(authUser.id);
      const teamInsurance = await team.related('team_insurance_companies')
        .query()
        .where((query) => {
          query.where('is_active', true).orWhere('is_active', false);
        })
        .where((query) => {
          query.where('status', 'pending').orWhere('status', 'validated');
        })
        .first();
      if (!teamInsurance) {
        apiResponse.message = "Compagnie d'assurance introuvable";
        return response.status(404).json(apiResponse);
      }
      const guaranteeTypes = await GuaranteeType.query().preload('guarantees', (query) => {
        query.where('is_active', true)
          .where((subQuery) => {
            subQuery.whereNull('insurance_company_id')
              .orWhere('insurance_company_id', teamInsurance.insuranceCompanyId)
          });
      });
      apiResponse.success = true;
      apiResponse.message = "Types de garanties récupérés avec succès";
      apiResponse.result = guaranteeTypes;
      return response.status(200).json(apiResponse);
    } catch (error) {
      apiResponse.message = "Une erreur est survenue lors de la récupération des types de garanties, veuillez reessayer plus tard";
      apiResponse.except = error.message;
      return response.status(500).json(apiResponse);
    }
  }

  public async getInsurancePackages({ response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
    }
    try {

      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté. Veuillez vous authentifier pour accéder à cette ressource.";
        return response.status(401).json(apiResponse);
      }
      const team = await this.getTeamByManager(authUser.id);
      const teamInsurance = await team.related('team_insurance_companies')
        .query()
        .where((query) => {
          query.where('is_active', true).orWhere('is_active', false);
        })
        .where((query) => {
          query.where('status', 'pending').orWhere('status', 'validated');
        })
        .first();
      if (!teamInsurance) {
        apiResponse.message = "Compagnie d'assurance introuvable";
        return response.status(404).json(apiResponse);
      }

      const packages = await Package.query()
        .where('insurance_company_id', teamInsurance.insuranceCompanyId)
        .where('type', 'team')
        .where('visibility', 'public')
        .preload('guarantees', (query) => {
          query.where('guarantees.is_active', true)
            .where((subQuery) => {
              subQuery.whereNull('guarantees.insurance_company_id')
                .orWhere('guarantees.insurance_company_id', teamInsurance.insuranceCompanyId)
            })
            .preload('guarantee_type');
        });
      apiResponse.success = true;
      apiResponse.message = "Packs récupérés avec succès";
      apiResponse.result = packages;
      return response.status(200).json(apiResponse);

    } catch (error) {
      apiResponse.message = "Une erreur est survenue lors de la récupération des packs, veuillez reessayer plus tard";
      apiResponse.except = error.message;
      return response.status(500).json(apiResponse);
    }
  }


}
