{"name": "team-service", "version": "1.0.0", "private": true, "scripts": {"dev": "node ace serve --watch", "build": "node ace build --production", "start": "node server.js", "test": "node ace test"}, "devDependencies": {"@adonisjs/assembler": "^5.9.6", "@japa/preset-adonis": "^1.2.0", "@japa/runner": "^2.5.1", "@types/proxy-addr": "^2.0.3", "@types/source-map-support": "^0.5.10", "adonis-preset-ts": "^2.1.0", "pino-pretty": "^11.2.1", "typescript": "~4.6", "youch": "^3.3.3", "youch-terminal": "^2.2.3"}, "dependencies": {"@adonisjs/auth": "^8.2.3", "@adonisjs/core": "^5.9.0", "@adonisjs/lucid": "^18.4.0", "@adonisjs/repl": "^3.1.11", "crypto": "^1.0.1", "cuid": "^3.0.0", "luxon": "^3.4.4", "minio": "^8.0.5", "mysql2": "^3.10.2", "nats": "^2.29.3", "proxy-addr": "^2.0.7", "reflect-metadata": "^0.2.2", "source-map-support": "^0.5.21"}}