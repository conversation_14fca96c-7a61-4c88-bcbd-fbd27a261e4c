# Documentation du Flow de Négociation de Contrat d'Assurance

## Vue d'ensemble

Le système de négociation permet aux entreprises de négocier des contrats d'assurance avec les compagnies d'assurance à travers un processus structuré de propositions et contre-propositions jusqu'à l'approbation finale.

## Acteurs du Système

### 1. **Entreprise (Team)**

- Initie les négociations
- Peut **rejeter** ou **approuver** les propositions
- L'approbation finalise la négociation

### 2. **Compagnie d'Assurance**

- Répond aux propositions de l'entreprise
- Peut **rejeter** ou **accepter** les propositions
- L'acceptation n'est pas définitive (attend l'approbation de l'entreprise)

## États et Statuts

### Statuts de Négociation

- `pending` : En attente de réponse
- `approved` : Approuvée par l'entreprise (négociation finalisée)
- `rejected` : Rejetée définitivement
- `expired` : Expirée

### Statuts de Version

- `pending` : En attente de réponse
- `approved` : Approuvée par l'entreprise (`is_validated = true`)
- `rejected` : Rejetée
- `accepted` : Acceptée par la compagnie (`is_validated = false`)

## Règles de Gestion

### ✅ **Règle A** : Actions de la Compagnie

- La compagnie peut uniquement **REJETER** ou **ACCEPTER** une proposition
- Elle ne peut pas valider définitivement une négociation

### ✅ **Règle B** : Actions de l'Entreprise

- L'entreprise peut uniquement **REJETER** ou **APPROUVER** une proposition
- Seule l'approbation de l'entreprise finalise la négociation

### ✅ **Règle C** : Validation Finale

- L'acceptation par la compagnie ne met pas `is_validated = true`
- Seule l'approbation par l'entreprise met `is_validated = true`

## Cycle de Vie Complet

### **Étape 1 : Initiation**

```
Entreprise → Crée Version 01.0 (pending)
```

### **Étape 2 : Réponse Compagnie**

```
Compagnie → Rejette Version 01.0 + Crée Version 02.0 (pending)
OU
Compagnie → Accepte Version 01.0 (accepted)
```

### **Étape 3 : Réponse Entreprise**

```
Si rejetée par compagnie :
  Entreprise → Rejette Version 02.0 + Crée Version 03.0 (pending)
  OU
  Entreprise → Approuve Version 02.0 (approved + validated = true)

Si acceptée par compagnie :
  Entreprise → Approuve Version 01.0 (approved + validated = true)
```

### **Étape 4 : Finalisation**

```
Négociation → status = "approved"
Version finale → status = "approved" + is_validated = true
```

## Références Générées

### Format des Références

- **Négociation** : `NEG{CODE_COMPAGNIE}-{YYYYMMDD}-{SEQUENCE}`
  - Exemple : `NEGSUNU-20250125-000001`
- **Versions** : `{VERSION_MAJEURE}.{VERSION_PATCH}`
  - Exemples : `01.0`, `02.0`, `03.0`

### Logique de Génération

- **Séquence par compagnie** : Chaque compagnie a sa propre séquence quotidienne
- **Réinitialisation quotidienne** : La séquence repart à 1 chaque jour
- **Incrémentation automatique** : Les versions s'incrémentent automatiquement

## API Endpoints

### 1. **Initier une Négociation**

```http
POST /api/negotiations/initiate
```

**Payload :**

```json
{
  "team_group_id": 1,
  "insurance_company_id": 2,
  "annual_prime": 50000,
  "family_settings": {
    "max_dependents": 3,
    "partner_coverage": true,
    "children_age_limit": 25,
    "principal_plafond": 100000,
    "partner_plafond": 80000,
    "child_plafond": 60000
  },
  "guarantees": [
    {
      "type": 1,
      "coverage_rate": 80,
      "annual_limit": 500000,
      "comment": "Consultation générale"
    }
  ],
  "coverage_period": 12,
  "resume": "Proposition initiale pour couverture santé"
}
```

**Réponse :**

```json
{
  "success": true,
  "message": "Négociation initiée avec succès",
  "result": {
    "negotiation": {
      "id": 1,
      "reference": "NEGSUNU-20250125-000001",
      "status": "pending"
    },
    "version": {
      "id": 1,
      "version": "01.0",
      "status": "pending"
    }
  }
}
```

### 2. **Rejeter et Contre-proposer**

```http
POST /api/negotiations/reject-and-counter
```

**Payload :**

```json
{
  "negotiation_id": 1,
  "version_id": 2,
  "rejection_reason": "Prime trop élevée",
  "annual_prime": 45000,
  "family_settings": { /* nouveaux paramètres */ },
  "guarantees": [ /* nouvelles garanties */ ],
  "coverage_period": 12,
  "resume": "Contre-proposition avec prime réduite"
}
```

### 3. **Approuver une Proposition**

```http
POST /api/negotiations/approve
```

**Payload :**

```json
{
  "negotiation_id": 1,
  "version_id": 3,
  "approval_comment": "Proposition acceptée, termes satisfaisants"
}
```

**Réponse :**

```json
{
  "success": true,
  "message": "Proposition approuvée avec succès. La négociation est finalisée.",
  "result": {
    "negotiation_id": 1,
    "approved_version_id": 3,
    "status": "approved"
  }
}
```

### 4. **Historique de Négociation**

```http
GET /api/negotiations/history?negotiation_id=1
```

**Réponse :**

```json
{
  "success": true,
  "result": {
    "negotiation": {
      "id": 1,
      "reference": "NEGSUNU-20250125-000001",
      "status": "approved",
      "created_at": "2025-01-25T10:00:00Z",
      "approved_at": "2025-01-25T15:30:00Z"
    },
    "versions": [
      {
        "id": 1,
        "version": "01.0",
        "actor_type": "team_member",
        "status": "rejected",
        "created_at": "2025-01-25T10:00:00Z",
        "rejected_at": "2025-01-25T12:00:00Z"
      },
      {
        "id": 2,
        "version": "02.0",
        "actor_type": "insurance_manager",
        "status": "rejected",
        "created_at": "2025-01-25T12:00:00Z",
        "rejected_at": "2025-01-25T14:00:00Z"
      },
      {
        "id": 3,
        "version": "03.0",
        "actor_type": "team_member",
        "status": "approved",
        "is_validated": true,
        "created_at": "2025-01-25T14:00:00Z",
        "approved_at": "2025-01-25T15:30:00Z"
      }
    ]
  }
}
```

## Interface Utilisateur - Composants Suggérés

### 1. **Page Liste des Négociations**

- **Tableau** avec colonnes : Référence, Compagnie, Statut, Date création, Actions
- **Filtres** : Statut, Compagnie, Date
- **Bouton** "Nouvelle Négociation"
- **Pagination** pour les grandes listes

### 2. **Formulaire Nouvelle Négociation**

- **Sélection du groupe d'équipe** (dropdown)
- **Sélection de la compagnie d'assurance** (dropdown avec recherche)
- **Formulaire des paramètres** :
  - Prime annuelle (input numérique)
  - Paramètres famille (formulaire structuré)
  - Garanties (tableau dynamique)
  - Période de couverture (sélecteur)
- **Bouton** "Initier la Négociation"

### 3. **Page Détail Négociation**

- **En-tête** : Référence, Statut global, Compagnie, Dates
- **Timeline** : Historique des versions avec statuts visuels
- **Détails de chaque version** : Paramètres, commentaires, dates
- **Actions contextuelles** selon le statut :
  - Si version compagnie en pending → Boutons "Rejeter & Contre-proposer" / "Approuver"
  - Si négociation finalisée → Affichage en lecture seule

### 4. **Formulaire Contre-proposition**

- **Champs modifiables** : Prime, garanties, paramètres famille
- **Champ obligatoire** : Raison du rejet (textarea)
- **Champ optionnel** : Commentaire de la nouvelle proposition
- **Comparaison** : Affichage côte à côte des anciennes/nouvelles valeurs

### 5. **Modal d'Approbation**

- **Résumé** de la proposition à approuver
- **Champ optionnel** : Commentaire d'approbation
- **Avertissement** : "Cette action finalisera la négociation"
- **Boutons** : "Annuler" / "Confirmer l'approbation"

## États d'Interface

### **Badges de Statut**

```css
.status-pending { background: #f59e0b; color: white; } /* Orange */
.status-approved { background: #10b981; color: white; } /* Vert */
.status-rejected { background: #ef4444; color: white; } /* Rouge */
.status-accepted { background: #3b82f6; color: white; } /* Bleu */
```

### **Actions Disponibles**

```javascript
// Logique d'affichage des boutons
function getAvailableActions(negotiation, version) {
  if (negotiation.status === 'approved') {
    return []; // Négociation finalisée, aucune action
  }

  if (version.actor_type === 'insurance_manager' && version.status === 'pending') {
    return ['reject_and_counter', 'approve']; // Entreprise peut répondre
  }

  return []; // En attente de réponse de la compagnie
}
```

### **Indicateurs Visuels**

- **Timeline verticale** pour l'historique des versions
- **Icônes d'acteurs** : 🏢 Entreprise, 🏛️ Compagnie
- **Couleurs de statut** : Vert (approuvé), Rouge (rejeté), Orange (pending), Bleu (accepté)
- **Badges de validation** : ✅ Validé, ⏳ En attente

## Gestion d'Erreurs

### **Codes d'Erreur**

- `400` : Données de formulaire invalides
- `401` : Non authentifié
- `403` : Action non autorisée
- `404` : Négociation introuvable ou accès non autorisé
- `500` : Erreur serveur

### **Messages Utilisateur**

```javascript
const messages = {
  success: {
    initiate: "Négociation initiée avec succès",
    approve: "Proposition approuvée avec succès. La négociation est finalisée.",
    counter: "Contre-proposition créée avec succès"
  },
  error: {
    initiate: "Une erreur est survenue lors de l'initiation",
    approve: "Une erreur est survenue lors de l'approbation",
    counter: "Une erreur est survenue lors de la création de la contre-proposition",
    notFound: "Négociation introuvable",
    unauthorized: "Accès non autorisé"
  },
  confirmation: {
    approve: "Êtes-vous sûr de vouloir approuver cette proposition ? Cette action finalisera la négociation.",
    reject: "Êtes-vous sûr de vouloir rejeter cette proposition ?"
  }
}
```

## Workflow de Développement

### **Étapes d'Implémentation**

1. **Phase 1** : Créer les pages de base (liste, détail)
2. **Phase 2** : Implémenter le formulaire d'initiation
3. **Phase 3** : Ajouter les actions de réponse (rejeter/approuver)
4. **Phase 4** : Implémenter la timeline et l'historique
5. **Phase 5** : Ajouter les notifications et validations

### **Tests Recommandés**

- **Test d'initiation** : Créer une nouvelle négociation
- **Test de rejet** : Rejeter et faire une contre-proposition
- **Test d'approbation** : Approuver une proposition
- **Test d'historique** : Vérifier l'affichage de la timeline
- **Test d'erreurs** : Gérer les cas d'erreur

---

*Cette documentation couvre l'ensemble du flow de négociation et fournit tous les éléments nécessaires pour l'implémentation frontend.*
