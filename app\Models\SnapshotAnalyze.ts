import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class SnapshotAnalyze extends BaseModel {
  @column({ isPrimary: true, columnName: 'id' })
  public id: number

  @column({ columnName: 'package_snapshot_id' })
  public packageSnapshotId: number

  @column({ columnName: 'analyze_id' })
  public analyzeId: number

  @column({ columnName: 'public_price' })
  public publicPrice: number

  @column({ columnName: 'is_active' })
  public isActive: boolean = true

  @column.dateTime({ autoCreate: true, columnName: 'created_at' })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true, columnName: 'updated_at' })
  public updatedAt: DateTime
}
