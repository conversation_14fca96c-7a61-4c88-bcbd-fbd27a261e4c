import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo, BelongsTo } from '@ioc:Adonis/Lucid/Orm'
import User from './User' // Assuming User model exists and is correctly defined
import Patient from './Patient'

export default class Archive extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({columnName: 'patient_id'})
  public patientId: number

  @column({columnName: 'archived_byself'})
  public archivedByself: number

  @column({columnName: 'archived_by'})
  public archivedBy: number | null

  @column({columnName: 'name'})
  public name: string | null

  @column({columnName: 'description'})
  public description: string | null

  @column({columnName: 'type'})
  public type: 'consultation' | 'examen' | 'medical' | 'other'

  @column({columnName: 'archived_at'})
  public archivedAt: string | null

  @column({columnName: 'metadata'})
  public metadata: any | null

  @column({columnName: 'documents'})
  public documents: any | null

  @column({columnName: 'uuid'})
  public uuid: string

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  // Relationships
  @belongsTo(() => User, {
    foreignKey: 'archivedBy', // Maps archivedBy to User
    localKey: 'id'
  })
  public archiver: BelongsTo<typeof User>

  @belongsTo(() => Patient, {
    foreignKey: 'patientId', // Maps patientId to User
    localKey: 'id'
  })
  public patient: BelongsTo<typeof Patient>
}
