import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

import HelperController from "../helpers/HelperController";
import { ApiResponse, UserStatus } from 'App/Controllers/interfaces';
import { rules, schema } from '@ioc:Adonis/Core/Validator';
import Database from '@ioc:Adonis/Lucid/Database';
import User from 'App/Models/User';
import Patient from 'App/Models/Patient';
import { DateTime } from 'luxon';
import TeamMember from 'App/Models/TeamMember';
import Wallet from 'App/Models/Wallet';

export default class MemberController extends HelperController {

  public async getTeamGroups({ response, auth }: HttpContextContract) {
    const apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
    };

    try {
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté. Veuillez vous authentifier pour accéder à cette ressource.";
        return response.status(401).json(apiResponse);
      }

      const team = await this.getTeamByManager(authUser.id);
      const groups = await team.related('groups').query()
        .withCount('members') 
        .orderBy('created_at', 'desc');

      // Formater le résultat si nécessaire
      const formattedGroups = groups.map(group => ({
        ...group.toJSON(),
        members_count: group.$extras.members_count
      }));

      apiResponse.success = true;
      apiResponse.message = "Groupes récupérés avec succès";
      apiResponse.result = formattedGroups;
      return response.status(200).json(apiResponse);

    } catch (error) {
      apiResponse.message = "Une erreur est survenue lors de la récupération des groupes";
      apiResponse.except = error.message;
      return response.status(500).json(apiResponse);
    }
  }

  public async getTeamGroupDetails({ response, auth, request }: HttpContextContract) {
    const apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
    };

    try {
      const team_group_id = request.input('team_group_id');
      if (!team_group_id) {
        apiResponse.message = "Veuillez fournir un identifiant de groupe";
        return response.status(400).json(apiResponse);
      }
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté. Veuillez vous authentifier pour accéder à cette ressource.";
        return response.status(401).json(apiResponse);
      }

      const team = await this.getTeamByManager(authUser.id);

      // Récupérer le groupe sans charger tous les membres
      const group = await team.related('groups').query()
        .where('id', team_group_id)
        .first();

      if (!group) {
        apiResponse.message = "Groupe introuvable";
        return response.status(404).json(apiResponse);
      }

      // Calculer les statistiques avec des requêtes d'agrégation optimisées
      const currentYear = new Date().getFullYear();

      // Requêtes séparées pour chaque statistique
      const totalMembers = await group.related('members').query().count('* as total');
      const activeMembers = await group.related('members').query()
        .whereNull('left_at')
        .where('coverage_status', 'active')
        .count('* as total');
      const coveredMembers = await group.related('members').query()
        .where('coverage_status', 'active')
        .whereNull('left_at')
        .count('* as total');
      const pendingMembers = await group.related('members').query()
        .where('coverage_status', 'pending')
        .whereNull('left_at')
        .count('* as total');
      const totalDependents = await group.related('members').query()
        .sum('dependent_children as total');

      // Requête pour l'âge moyen - approche plus simple
      const ageResult = await Database
        .rawQuery(`
          SELECT AVG(? - patients.birthday_year) as average_age
          FROM team_members
          INNER JOIN patients ON team_members.patient_id = patients.id
          WHERE team_members.team_group_id = ?
          AND patients.birthday_year IS NOT NULL
        `, [currentYear, team_group_id]);

      const ageStats = ageResult[0] && ageResult[0].length > 0 ? ageResult[0][0] : null;

      const stats = {
        totalMembers: parseInt(totalMembers[0].$extras.total) || 0,
        activeMembers: parseInt(activeMembers[0].$extras.total) || 0,
        coveredMembers: parseInt(coveredMembers[0].$extras.total) || 0,
        pendingMembers: parseInt(pendingMembers[0].$extras.total) || 0,
        averageAge: ageStats && ageStats.average_age ? Math.round(parseFloat(ageStats.average_age)) : 0,
        totalDependents: parseInt(totalDependents[0].$extras.total) || 0
      };

      apiResponse.success = true;
      apiResponse.message = "Groupe récupéré avec succès";
      apiResponse.result = {
        group,
        stats
      };
      return response.status(200).json(apiResponse);

    } catch (error) {
      apiResponse.message = "Une erreur est survenue lors de la récupération des informations de l'utilisateur, veuillez reessayer plus tard";
      apiResponse.except = error.message;
      return response.status(500).json(apiResponse);
    }
  }

  public async getMembers({ response, auth, request }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
    }
    try {
      const page = request.input('page', 1);
      const limit = request.input('limit', 10);
      const group_id = request.input('group_id', null);
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté. Veuillez vous authentifier pour accéder à cette ressource.";
        return response.status(401).json(apiResponse);
      }
      const team = await this.getTeamByManager(authUser.id);
      if (group_id) {
        const group = await team.related('groups').query().where('id', group_id).first();
        if (!group) {
          apiResponse.message = "Groupe introuvable";
          return response.status(404).json(apiResponse);
        }
      }
      const query = team.related('members').query().preload('patient');
      if (group_id) {
        query.where('group_id', group_id);
      }
      const members = await query.paginate(page, limit);
      apiResponse.result = members;
      apiResponse.success = true;
      return response.status(200).json(apiResponse);
    } catch (error) {
      apiResponse.message = "Une erreur est survenue lors de la récupération des informations de l'utilisateur, veuillez reessayer plus tard";
      apiResponse.except = error.message;
      return response.status(401).json(apiResponse);
    }
  }

  public async getMemberDetails({ request, auth, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
    }
    try {
      const member_id = request.input('member_id');
      if (!member_id) {
        apiResponse.message = "ID de membre requis";
        return response.status(400).json(apiResponse);
      }
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté. Veuillez vous authentifier pour accéder à cette ressource.";
        return response.status(401).json(apiResponse);
      }
      const team = await this.getTeamByManager(authUser.id);
      const member = await TeamMember.query().where('team_id', team.id).where('id', member_id).preload('team_group').preload('patient', (query) => {
        query.preload('user').preload('country').preload('city').preload('quarter');
      }).first();
      if (!member) {
        apiResponse.message = "Membre introuvable";
        return response.status(404).json(apiResponse);
      }
      apiResponse.message = "Membre récupéré avec succès";
      apiResponse.result = member;
      apiResponse.success = true;
      return response.status(200).json(apiResponse);
    } catch (error) {
      apiResponse.message = "Une erreur est survenue lors de la récupération des informations de l'utilisateur, veuillez reessayer plus tard";
      apiResponse.except = error.message;
      return response.status(401).json(apiResponse);
    }
  }

  public async addTeamGroup({ response, auth, request }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
    }
    try {
      const payload = await request.validate({
        schema: schema.create({
          name: schema.string(),
          code: schema.string.optional(),
          description: schema.string.optional(),
        }),
        messages: {
          'name.string': "Le nom du groupe doit être une chaîne de caractères",
          'code.string': "Le code du groupe doit être une chaîne de caractères",
          'description.string': "La description du groupe doit être une chaîne de caractères",
        }
      });

      const { name, code, description } = payload;

      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté. Veuillez vous authentifier pour accéder à cette ressource.";
        return response.status(401).json(apiResponse);
      }
      const team = await this.getTeamByManager(authUser.id);

      const checkGroup = await team.related('groups').query().where('name', name).first();
      if (checkGroup && checkGroup.name && checkGroup.name === name) {
        apiResponse.message = "Un groupe avec ce nom existe déjà";
        return response.status(400).json(apiResponse);
      }

      if (checkGroup && checkGroup.code && checkGroup.code === code) {
        apiResponse.message = "Un groupe avec ce code existe déjà";
        return response.status(400).json(apiResponse);
      }
      let groupCode = code;
      if (!code) {
        groupCode = Math.floor(1000000 + Math.random() * 9000000).toString();
      }

      const newGroup = await team.related('groups').create({
        name: name,
        code: groupCode,
        description: description,
        publicId: await this.generateUUID(),
      });

      apiResponse.success = true;
      apiResponse.message = "Groupe créé avec succès";
      apiResponse.result = newGroup;
      return response.status(200).json(apiResponse);
    } catch (error) {
      apiResponse.message = "Une erreur est survenue lors de la récupération des informations de l'utilisateur, veuillez reessayer plus tard";
      apiResponse.except = error.message;
      return response.status(401).json(apiResponse);
    }
  }

  public async addMember({ response, auth, request }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
    }
    try {
      const payload = await request.validate({
        schema: schema.create({
          role: schema.enum(['hr_manager', 'member']),
          first_name: schema.string(),
          last_name: schema.string(),
          email: schema.string(),
          phone: schema.string(),
          gender: schema.enum(['M', 'F']),
          birthday_year: schema.number(),
          birthday_month: schema.number(),
          birthday_day: schema.number(),
          country_id: schema.number(),
          city_id: schema.number(),
          profession: schema.string.optional(),
          marital_status: schema.enum.optional(['single', 'married', 'divorced', 'widowed']),
          employee_matricule: schema.string.optional(),
          dependent_children: schema.number.optional(),
          team_group_id: schema.number()
        }),
        messages: {
          'firstName.string': "Le prénom du membre doit être une chaîne de caractères",
          'lastName.string': "Le nom du membre doit être une chaîne de caractères",
          'email.string': "L'email du membre doit être une chaîne de caractères",
          'phone.string': "Le numéro de téléphone du membre doit être une chaîne de caractères",
          'gender.enum': "Le genre du membre doit être une chaîne de caractères",
          'birthdayYear.number': "L'année de naissance du membre doit être un nombre",
          'birthdayMonth.number': "Le mois de naissance du membre doit être un nombre",
          'birthdayDay.number': "Le jour de naissance du membre doit être un nombre",
        }
      });

      const {
        role, first_name, last_name, email, phone, gender, birthday_year, birthday_month, birthday_day, country_id, city_id,
        profession, marital_status, employee_matricule, dependent_children, team_group_id
      } = payload;

      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté. Veuillez vous authentifier pour accéder à cette ressource.";
        return response.status(401).json(apiResponse);
      }
      const team = await this.getTeamByManager(authUser.id);

      const checkUser = await User.query().where('email', email).orWhere('phone', phone).first();
      if (checkUser) {
        apiResponse.message = "Un compte existe déjà avec cet email ou ce numéro de téléphone. Veuillez utiliser d'autres coordonnées.";
        return response.status(400).json(apiResponse);
      }

      const checkPatient = await Patient.query().where('email', email).orWhere('phone', phone).first();
      if (checkPatient) {
        apiResponse.message = "Un compte existe déjà avec cet email ou ce numéro de téléphone. Veuillez utiliser d'autres coordonnées.";
        return response.status(400).json(apiResponse);
      }

      const trx = await Database.transaction();
      try {
        let codeP = await this.generateCodeParrainage(8);
        const parrainage = {
          create_account: 0,
          active_qrcode: 0,
          adhesion_fees: 0,
          plan: 1,
          activeMoney: false
        }
        let username = first_name.split(' ').join(' ') + ' ' + last_name.toLocaleUpperCase();
        const newUser = await User.create({
          username: username,
          email: email,
          phone: phone,
          password: phone,
          countryId: country_id,
          languageId: 1,
          roleId: 6,
          creatorId: authUser.id,
          status: UserStatus.Actived,
          activatedAt: DateTime.now(),
          phoneVerifiedAt: DateTime.now(),
          codeParrainage: codeP,
          parrainage: JSON.stringify(parrainage),
        }, { client: trx });

        if (!newUser) {
          await trx.rollback();
          apiResponse.message = "Echec de création du compte utilisateur, une erreur serveur s'est produite";
          return response.status(500).json(apiResponse);
        }

        let codePatient = await this.generateUUID();
        const newPatient = await Patient.create({
          first_name: first_name,
          last_name: last_name,
          email: email,
          phone: phone,
          gender: gender,
          birthday_year: birthday_year,
          birthday_month: birthday_month,
          birthday_day: birthday_day,
          user_id: newUser.id,
          status: 'activated',
          code: codePatient,
          country_id: country_id,
          city_id: city_id,
          situation_matrimoniale: marital_status,
          profession: profession,
        }, { client: trx });

        if (!newPatient) {
          await trx.rollback();
          apiResponse.message = "Echec de création du compte patient, une erreur serveur s'est produite";
          return response.status(500).json(apiResponse);
        }

        const newWallet = await newUser.related('wallet').create({
          code: await this.generateWalletCode(),
          balance: 0,
          ownerType: 'patient',
          ownerId: newPatient.id,
          libelle: "DO WALLET",
          typeWalletId: 2,
        }, { client: trx });

        if (!newWallet) {
          await trx.rollback();
          apiResponse.message = "Echec de création du compte patient, une erreur serveur s'est produite";
          return response.status(500).json(apiResponse);
        }

        const newTeamMember = await team.related('members').create({
          patientId: newPatient.id,
          publicId: await this.generateUUID(),
          role: role as 'admin' | 'hr_manager' | 'member',
          employeeMatricule: employee_matricule,
          maritalStatus: marital_status as 'single' | 'married' | 'divorced' | 'widowed',
          dependentChildren: dependent_children,
          joinedAt: DateTime.now(),
          teamGroupId: team_group_id
        }, { client: trx });

        if (!newTeamMember) {
          await trx.rollback();
          apiResponse.message = "Echec de l'ajout du membre à l'équipe, une erreur serveur s'est produite";
          return response.status(500).json(apiResponse);
        }

        const employee_count = team.employeeCount ? team.employeeCount + 1 : 1;
        await team.merge({ employeeCount: employee_count }).useTransaction(trx).save();

        await trx.commit();
        apiResponse.success = true;
        apiResponse.message = "Membre ajouté avec succès";
        apiResponse.result = {
          user: newUser,
          patient: newPatient,
          teamMember: newTeamMember
        };
        return response.status(201).json(apiResponse);
      } catch (error) {
        console.log("error addMember", error);
        await trx.rollback();
        apiResponse.message = "Echec de l'ajout du membre, une erreur serveur s'est produite";
        apiResponse.except = error;
        return response.status(500).json(apiResponse);
      }
    } catch (error) {
      console.log("error addMember", error);
      apiResponse.message = "Une erreur est survenue lors de l'ajout du membre, veuillez reessayer plus tard";
      apiResponse.except = error.message;
      return response.status(500).json(apiResponse);
    }
  }

  public async updateMember({ response, auth, request }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
    }
    try {
      const payload = await request.validate({
        schema: schema.create({
          member_id: schema.number(),
          role: schema.enum.optional(['hr_manager', 'member']),
          first_name: schema.string.optional(),
          last_name: schema.string.optional(),
          email: schema.string.optional(),
          phone: schema.string.optional(),
          gender: schema.enum.optional(['M', 'F']),
          birthday_year: schema.number.optional(),
          birthday_month: schema.number.optional(),
          birthday_day: schema.number.optional(),
          country_id: schema.number.optional(),
          city_id: schema.number.optional(),
          profession: schema.string.optional(),
          marital_status: schema.enum.optional(['single', 'married', 'divorced', 'widowed']),
          employee_matricule: schema.string.optional(),
          dependent_children: schema.number.optional(),
        }),
        messages: {
          'firstName.string': "Le prénom du membre doit être une chaîne de caractères",
          'lastName.string': "Le nom du membre doit être une chaîne de caractères",
          'email.string': "L'email du membre doit être une chaîne de caractères",
          'phone.string': "Le numéro de téléphone du membre doit être une chaîne de caractères",
          'gender.enum': "Le genre du membre doit être une chaîne de caractères",
          'birthdayYear.number': "L'année de naissance du membre doit être un nombre",
          'birthdayMonth.number': "Le mois de naissance du membre doit être un nombre",
          'birthdayDay.number': "Le jour de naissance du membre doit être un nombre",
        }
      });

      const {
        member_id, role, first_name, last_name, email, phone, gender, birthday_year, birthday_month, birthday_day, country_id, city_id,
        profession, marital_status, employee_matricule, dependent_children
      } = payload;

      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté. Veuillez vous authentifier pour accéder à cette ressource.";
        return response.status(401).json(apiResponse);
      }
      const team = await this.getTeamByManager(authUser.id);

      const teamMember = await team.related('members').query().where('id', member_id).first();
      if (!teamMember) {
        apiResponse.message = "Membre introuvable";
        return response.status(404).json(apiResponse);
      }

      const patient = await teamMember.related('patient').query().first();
      if (!patient) {
        apiResponse.message = "Membre introuvable";
        return response.status(404).json(apiResponse);
      }

      const user = await patient.related('user').query().first();
      if (!user) {
        apiResponse.message = "Membre introuvable";
        return response.status(404).json(apiResponse);
      }

      const trx = await Database.transaction();
      try {
        patient.merge({
          first_name: first_name || patient.first_name,
          last_name: last_name || patient.last_name,
          gender: gender ? (gender as 'M' | 'F') : patient.gender,
          birthday_year: birthday_year || patient.birthday_year,
          birthday_month: birthday_month || patient.birthday_month,
          birthday_day: birthday_day || patient.birthday_day,
          country_id: country_id || patient.country_id,
          city_id: city_id || patient.city_id,
          profession: profession || patient.profession,
          situation_matrimoniale: marital_status ? (marital_status as 'single' | 'married' | 'divorced' | 'widowed') : patient.situation_matrimoniale,
        });
        await patient.save();

        teamMember.merge({
          role: role ? (role as 'admin' | 'hr_manager' | 'member') : teamMember.role,
          employeeMatricule: employee_matricule || teamMember.employeeMatricule,
          maritalStatus: marital_status ? (marital_status as 'single' | 'married' | 'divorced' | 'widowed') : teamMember.maritalStatus,
          dependentChildren: dependent_children || teamMember.dependentChildren,
        });
        await teamMember.save();

        user.merge({
          email: email || user.email,
          phone: phone || user.phone,
        });
        await user.save();

        await trx.commit();
        apiResponse.success = true;
        apiResponse.message = "Membre mis à jour avec succès";
        apiResponse.result = {
          user: user,
          patient: patient,
          teamMember: teamMember
        };
        return response.status(200).json(apiResponse);
      } catch (error) {
        await trx.rollback();
        apiResponse.message = "Echec de la mise à jour du membre, une erreur serveur s'est produite";
        apiResponse.except = error;
        return response.status(500).json(apiResponse);
      }
    } catch (error) {
      apiResponse.message = "Une erreur est survenue lors de la mise à jour du membre, veuillez reessayer plus tard";
      apiResponse.except = error.message;
      return response.status(500).json(apiResponse);
    }
  }

  public async importMembers({ request, auth, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
    }
    try {
      const payload = await request.validate({
        schema: schema.create({
          team_group_id: schema.number(),
          members: schema.array().members(schema.object().members({
            first_name: schema.string(),
            last_name: schema.string(),
            email: schema.string(),
            phone: schema.number(),
            gender: schema.enum(['M', 'F']),
            birthday_year: schema.number(),
            birthday_month: schema.number(),
            birthday_day: schema.number(),
            profession: schema.string.optional(),
            marital_status: schema.enum.optional(['single', 'married', 'divorced', 'widowed']),
            employee_matricule: schema.string.optional(),
            dependent_children: schema.number.optional(),
            role: schema.enum.optional(['hr_manager', 'member']),
          })),
        }),
        messages: {
          'team_group_id.number': "L'identifiant du groupe doit être un nombre",
          'members.array': "La liste des membres doit être un tableau",
          'members.*.first_name.string': "Le prénom doit être une chaîne de caractères",
          'members.*.last_name.string': "Le nom doit être une chaîne de caractères",
          'members.*.email.string': "L'email doit être une chaîne de caractères",
          'members.*.phone.number': "Le téléphone doit être un nombre",
          'members.*.gender.enum': "Le genre doit être M ou F",
          'members.*.birthday_year.number': "L'année de naissance doit être un nombre",
          'members.*.birthday_month.number': "Le mois de naissance doit être un nombre",
          'members.*.birthday_day.number': "Le jour de naissance doit être un nombre",
        }
      });

      const { team_group_id, members } = payload;

      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté. Veuillez vous authentifier pour accéder à cette ressource.";
        return response.status(401).json(apiResponse);
      }
      const team = await this.getTeamByManager(authUser.id);

      const group = await team.related('groups').query()
        .where('id', team_group_id)
        .first();

      if (!group) {
        apiResponse.message = "Groupe introuvable";
        return response.status(404).json(apiResponse);
      }

      // Validation des doublons dans les données d'entrée
      const emails = members.map(m => m.email);
      const phones = members.map(m => m.phone);
      const duplicateEmails = emails.filter((email, index) => emails.indexOf(email) !== index);
      const duplicatePhones = phones.filter((phone, index) => phones.indexOf(phone) !== index);

      if (duplicateEmails.length > 0) {
        apiResponse.message = `Emails en doublon détectés: ${duplicateEmails.join(', ')}`;
        return response.status(400).json(apiResponse);
      }

      if (duplicatePhones.length > 0) {
        apiResponse.message = `Numéros de téléphone en doublon détectés: ${duplicatePhones.join(', ')}`;
        return response.status(400).json(apiResponse);
      }

      // Vérification des utilisateurs/patients existants
      const existingUsers = await User.query()
        .whereIn('email', emails)
        .orWhereIn('phone', phones);

      const existingPatients = await Patient.query()
        .whereIn('email', emails)
        .orWhereIn('phone', phones);

      if (existingUsers.length > 0 || existingPatients.length > 0) {
        const existingEmails = [...existingUsers.map(u => u.email), ...existingPatients.map(p => p.email)];
        const existingPhones = [...existingUsers.map(u => u.phone), ...existingPatients.map(p => p.phone)];
        apiResponse.message = `Des comptes existent déjà avec ces coordonnées: ${[...new Set([...existingEmails, ...existingPhones])].join(', ')}`;
        return response.status(400).json(apiResponse);
      }

      const trx = await Database.transaction();
      try {
        // Préparer les données pour les insertions en lot
        const usersData: any[] = [];
        const patientsData: any[] = [];
        const teamMembersData: any[] = [];
        const walletsData: any[] = [];

        for (const member of members) {
          const codeP = await this.generateCodeParrainage(8);
          const parrainage = {
            create_account: 0,
            active_qrcode: 0,
            adhesion_fees: 0,
            plan: 1,
            activeMoney: false
          };
          const username = member.first_name + ' ' + member.last_name.toUpperCase();

          usersData.push({
            username: username,
            email: member.email,
            phone: member.phone,
            password: member.phone.toString(), // Comme dans addMember
            countryId: Number(team.countryId),
            languageId: 1,
            roleId: 6, // Comme dans addMember
            creatorId: authUser.id,
            status: UserStatus.Actived, // Utiliser la valeur string directement
            activatedAt: DateTime.local(),
            phoneVerifiedAt: DateTime.local(),
            codeParrainage: codeP,
            parrainage: JSON.stringify(parrainage),
          });
        }

        // Créer tous les utilisateurs en une fois
        const newUsers = await User.createMany(usersData, { client: trx });
        if (!newUsers) {
          await trx.rollback();
          apiResponse.message = "Echec de l'ajout du membre à l'équipe, une erreur serveur s'est produite";
          return response.status(500).json(apiResponse);
        }

        // Préparer les données des patients avec les IDs des utilisateurs créés
        for (let i = 0; i < members.length; i++) {
          const member = members[i];
          const newUser = newUsers[i];
          const codePatient = await this.generateUUID();

          patientsData.push({
            first_name: member.first_name,
            last_name: member.last_name,
            email: member.email,
            phone: member.phone,
            gender: member.gender,
            birthday_year: Number(member.birthday_year),
            birthday_month: Number(member.birthday_month),
            birthday_day: Number(member.birthday_day),
            user_id: Number(newUser.id),
            status: 'activated',
            code: codePatient,
            country_id: Number(team.countryId),
            city_id: Number(team.cityId),
            profession: member.profession || null,
            situation_matrimoniale: member.marital_status || null,
          });

          walletsData.push({
            user_id: Number(newUser.id),
            code: await this.generateWalletCode(),
            balance: 0,
            owner_type: 'patient',
            owner_id: null,
            libelle: "DO WALLET",
            type_wallet_id: 2,
          });
        }

        // Créer tous les patients en une fois
        const newPatients = await Patient.createMany(patientsData, { client: trx });
        if (!newPatients) {
          await trx.rollback();
          apiResponse.message = "Echec d'importation des membres, une erreur serveur s'est produite";
          return response.status(500).json(apiResponse);
        }

        // Mettre à jour les wallets avec les IDs des patients et les créer
        for (let i = 0; i < walletsData.length; i++) {
          walletsData[i].owner_id = Number(newPatients[i].id);
        }
        await Database.table('wallets').useTransaction(trx).multiInsert(walletsData);

        // Préparer les données des membres d'équipe
        for (let i = 0; i < members.length; i++) {
          const member = members[i];
          const newPatient = newPatients[i];

          teamMembersData.push({
            teamId: Number(team.id),
            teamGroupId: Number(group.id),
            patientId: Number(newPatient.id),
            publicId: await this.generateUUID(),
            role: 'member',
            employeeMatricule: member.employee_matricule || null,
            maritalStatus: member.marital_status || null,
            dependentChildren: Number(member.dependent_children || 0),
            createBySelf: false,
            joinedAt: new Date(),
            createdAt: new Date(),
            updatedAt: new Date(),
          });
        }

        // Créer tous les membres d'équipe en une fois
        await Database.table('team_members').useTransaction(trx).multiInsert(teamMembersData);

        const employee_count = team.employeeCount ? team.employeeCount + members.length : members.length;
        await team.merge({ employeeCount: employee_count }).useTransaction(trx).save();

        await trx.commit();
        apiResponse.success = true;
        apiResponse.message = `${members.length} membres importés avec succès`;
        apiResponse.result = {
          imported_count: members.length,
          users: newUsers.length,
          patients: newPatients.length
        };
        return response.status(201).json(apiResponse);

      } catch (error) {
        console.log("error importMembers", error);
        await trx.rollback();
        apiResponse.message = "Echec de l'ajout du membre à l'équipe, une erreur serveur s'est produite";
        apiResponse.except = error;
        return response.status(500).json(apiResponse);
      }
    } catch (error) {
      apiResponse.message = "Une erreur est survenue lors de la mise à jour du membre, veuillez reessayer plus tard";
      apiResponse.except = error.message;
      console.log("error importMembers", error);
      apiResponse.errors = error.messages;
      return response.status(500).json(apiResponse);
    }
  }

  public async importMembersV2({ request, auth, response }: HttpContextContract) {

    try {

      const payload = await request.validate({
        schema: schema.create({
          team_group_id: schema.number(),
          members: schema.array().members(
            schema.object().members({
              first_name: schema.string(),
              last_name: schema.string(),
              email: schema.string({}, [
                rules.unique({ table: 'users', column: 'email' }),
                rules.unique({ table: 'patients', column: 'email' }),
              ]),
              phone: schema.string({}, [
                rules.unique({ table: 'users', column: 'phone' }),
                rules.unique({ table: 'patients', column: 'phone' }),
              ]),
              gender: schema.enum(['M', 'F']),
              birthday_year: schema.number(),
              birthday_month: schema.number(),
              birthday_day: schema.number(),
              profession: schema.string.optional(),
              marital_status: schema.enum.optional(['single', 'married', 'divorced', 'widowed'] as const),
              employee_matricule: schema.string.optional(),
              dependent_children: schema.number.optional(),
              role: schema.enum.optional(['hr_manager', 'member'] as const),
            })
          ),
        }),
      });

      const authUser = await auth.authenticate();
      if (!authUser) {
        return response.status(401).json({
          success: false,
          message: "Vous n'êtes pas connecté. Veuillez vous authentifier pour accéder à cette ressource.",
          except: "Unauthorized",
          result: null,
        });
      }

      const team = await this.getTeamByManager(authUser.id);
      const group = await team
        .related('groups')
        .query()
        .where('id', payload.team_group_id)
        .firstOrFail();

      const now = DateTime.local();
      const parrainageTpl = JSON.stringify({
        create_account: 0,
        active_qrcode: 0,
        adhesion_fees: 0,
        plan: 1,
        activeMoney: false,
      });

      /* -------------------------------------------------
     * 3. Préparation des tableaux
     * ------------------------------------------------- */
      const usersData: Partial<User>[] = [];
      const patientsData: Partial<Patient>[] = [];
      const walletsData: Partial<Wallet>[] = [];
      const teamMembersData: Partial<TeamMember>[] = [];

      for (const m of payload.members) {
        const username = `${m.first_name} ${m.last_name.toUpperCase()}`;
        usersData.push({
          username,
          email: m.email,
          phone: m.phone,
          password: m.phone.toString(),
          countryId: Number(team.countryId),
          languageId: 1,
          roleId: 6,
          creatorId: authUser.id,
          status: UserStatus.Actived,
          activatedAt: now,
          phoneVerifiedAt: now,
          codeParrainage: await this.generateCodeParrainage(8),
          parrainage: parrainageTpl,
        });
      }

      /* -------------------------------------------------
       * 4. Transaction unique avec createMany
       * ------------------------------------------------- */
      try {

        await Database.transaction(async (trx) => {
          // 1) Users
          const users = await User.createMany(usersData, { client: trx });

          // 2) Patients
          for (let i = 0; i < users.length; i++) {
            const m = payload.members[i];
            patientsData.push({
              first_name: m.first_name,
              last_name: m.last_name,
              email: m.email,
              phone: m.phone,
              gender: m.gender,
              birthday_year: m.birthday_year,
              birthday_month: m.birthday_month,
              birthday_day: m.birthday_day,
              user_id: users[i].id,
              status: 'activated',
              code: await this.generateUUID(),
              country_id: team.countryId,
              city_id: team.cityId,
              profession: m.profession || null,
              situation_matrimoniale: m.marital_status || null,
            });
          }
          const patients = await Patient.createMany(patientsData, { client: trx });

          // 3) Wallets
          for (let i = 0; i < patients.length; i++) {
            walletsData.push({
              userId: users[i].id,
              ownerType: 'patient',
              ownerId: patients[i].id,
              code: await this.generateWalletCode(),
              balance: 0,
              libelle: 'DO WALLET',
              typeWalletId: 2,
            });
          }
          await Wallet.createMany(walletsData, { client: trx });

          // 4) TeamMembers
          for (let i = 0; i < patients.length; i++) {
            const m = payload.members[i];
            teamMembersData.push({
              teamId: team.id,
              teamGroupId: group.id,
              patientId: patients[i].id,
              publicId: await this.generateUUID(),
              role: m.role || 'member',
              employeeMatricule: m.employee_matricule?.toString() || null,
              maritalStatus: m.marital_status as 'single' | 'married' | 'divorced' | 'widowed',
              dependentChildren: m.dependent_children || 0,
              createBySelf: false,
              joinedAt: now,
            });
          }
          await TeamMember.createMany(teamMembersData, { client: trx });

          // 5) Mise à jour compteur d'employés
          await team
            .useTransaction(trx)
            .merge({ employeeCount: (team.employeeCount || 0) + payload.members.length })
            .save();
        });

        return response.status(201).json({
          success: true,
          message: `${payload.members.length} membres importés avec succès`,
          result: {
            imported_count: payload.members.length,
          },
        });
      } catch (error) {
        console.log(error);
        return response.status(500).json({
          success: false,
          message: 'Une erreur est survenue lors de l\'importation des membres',
          except: error.message,
          errors: error.messages,
        });
      }
    } catch (error) {
      console.log(error);
      return response.status(500).json({
        success: false,
        message: 'Une erreur est survenue lors de l\'importation des membres',
        except: error.message,
        errors: error.messages,
      });
    }
  }


}
