import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class TeamMemberAdjustment extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'team_member_id' })
  public teamMemberId: number

  @column({ columnName: 'adjusted_by' })
  public adjustedBy: number

  @column()
  public type: 'plafond' | 'taux' | 'coverage'

  @column({ columnName: 'old_value' })
  public oldValue: number | null

  @column({ columnName: 'new_value' })
  public newValue: number | null

  @column()
  public reason: string | null

  @column.dateTime({ columnName: 'effective_from' })
  public effectiveFrom: DateTime | null

  @column.dateTime({ columnName: 'effective_to' })
  public effectiveTo: DateTime | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
