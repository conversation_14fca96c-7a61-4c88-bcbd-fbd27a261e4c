import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import Transaction from './Transaction'
import { Payment } from './Payment'

export default class WalletHistory extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'wallet_id' })
  public walletId: number

  @column({ columnName: 'transaction_id' })
  public transactionId: number | null

  @column({columnName: 'payment_id'})
  public paymentId: number | null

  @column({ columnName: 'type' })
  public type: 'deposit' | 'withdrawal' | 'payment' | 'transaction' | 'transfert'

  @column()
  public amount: number

  @column({ columnName: 'date_op' })
  public dateOp: Date | null

  @column()
  public description: string

  @column()
  public metadata: string

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(()=>Transaction,{
    foreignKey: 'transactionId',
    localKey: 'id'
  })
  public transaction: BelongsTo<typeof Transaction>

  @belongsTo(()=>Payment,{
    foreignKey: 'paymentId',
    localKey: 'id'
  })
  public payment: BelongsTo<typeof Payment>
}
