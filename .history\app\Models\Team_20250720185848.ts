import { DateTime } from 'luxon'
import { BaseModel, column, Has<PERSON>any, hasMany } from '@ioc:Adonis/Lucid/Orm'
import TeamMember from './TeamMember'
import TeamInsuranceCompany from './TeamInsuranceCompany'

export default class Team extends BaseModel {

  @column({ isPrimary: true, columnName: 'id' })
  public id: number

  @column({ columnName: 'public_id' })
  public publicId: string

  @column({ columnName: 'team_code' })
  public teamCode: string | null

  @column({ columnName: 'name' })
  public name: string

  @column({ columnName: 'legal_name' })
  public legalName: string | null

  @column({ columnName: 'registration_number' })
  public registrationNumber: string | null

  @column({ columnName: 'document' })
  public document: string | null

  @column({ columnName: 'type' })
  public type: 'company' | 'association' | 'collective' | 'other'

  @column({ columnName: 'industry' })
  public industry: string | null

  @column({ columnName: 'employee_count' })
  public employeeCount: number | null

  @column({ columnName: 'contact_email' })
  public contactEmail: string

  @column({ columnName: 'contact_phone' })
  public contactPhone: string

  @column({ columnName: 'address' })
  public address: string | null

  @column({ columnName: 'postal_code' })
  public postalCode: string | null

  @column({ columnName: 'city_id' })
  public cityId: number | null

  @column({ columnName: 'country_id' })
  public countryId: number | null

  @column({ columnName: 'location' })
  public location: any | null // Point type

  @column({ columnName: 'settings' })
  public settings: any | null // JSON type

  @column({ columnName: 'billing_info' })
  public billingInfo: any | null // JSON type

  @column({ columnName: 'status' })
  public status: 'active' | 'pending' | 'suspended' | 'archived'

  @column({ columnName: 'payment_status' })
  public paymentStatus: 'current' | 'late' | 'unpaid' | null

  @column({ columnName: 'insurance_status' })
  public insuranceStatus: 'pending' | 'active' | 'suspended' | 'expired'

  @column({ columnName: 'has_insurance' })
  public hasInsurance: boolean

  @column({ columnName: 'created_by' })
  public createdBy: number | null

  @column({ columnName: 'updated_by' })
  public updatedBy: number | null

  @column.dateTime({ autoCreate: true, columnName: 'created_at' })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true, columnName: 'updated_at' })
  public updatedAt: DateTime

  @column.dateTime({ columnName: 'deleted_at' })
  public deletedAt: DateTime | null

  @hasMany(() => TeamMember, {
    localKey: 'id',
    foreignKey: 'teamId',
  })
  public members: HasMany<typeof TeamMember>

  @hasMany(() => TeamInsuranceCompany, {
    localKey: 'id',
    foreignKey: 'teamId',
  })
  public insuranceContracts: HasMany<typeof TeamInsuranceCompany>
}
