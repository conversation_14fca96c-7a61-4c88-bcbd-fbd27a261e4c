import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column, ManyToMany, manyToMany } from '@ioc:Adonis/Lucid/Orm'
import Patient from './Patient'
import Soignant from './Soignant'
import HealthInstitute from './HealthInstitute'
import MedicalFacilitator from './MedicalFacilitator'
// import AppointmentService from './AppointmentService'
import Service from './Service'
import AppointmentType from './AppointmentType'
import { AppointmentStatus } from 'App/Controllers/interfaces'

export default class Appointment extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column({ columnName: 'patient_id' })
  public patientId: number

  @column({ columnName: 'health_institute_id' })
  public healthInstituteId: number | null

  @column({ columnName: 'hospital' })
  public hospital: string | null

  @column({ columnName: 'pro_id' })
  public proId: number | null

  @column({ columnName: 'doctor' })
  public doctor: any | null

  @column({ columnName: 'fmd_id' })
  public fmdId: number | null

  @column({columnName: 'appointment_type_id'})
  public appointmentTypeId: number | null

  @column({ columnName: 'type' })
  public type: 'hospital' | 'fmd' | 'home'

  @column({columnName: 'service_id'})
  public serviceId: number | null

  @column({ columnName: 'acte' })
  public acte: boolean

  @column({ columnName: 'phone' })
  public phone: string | null

  @column({ columnName: 'location' })
  public location: any| null

  @column({ columnName: 'cancel_reason' })
  public cancelReason: string | null

  @column({columnName: 'avis'})
  public avis: string | null

  @column({ columnName: 'start_date' })
  public startDate: Date | null

  @column({ columnName: 'end_date' })
  public endDate: Date | null

  @column({ columnName: 'confirmed_at' })
  public confirmedAt: Date | null

  @column({ columnName: 'canceled_at' })
  public canceledAt: Date | null

  @column({ columnName: 'paid_at' })
  public paidAt: Date | null

  @column({ columnName: 'is_paid' })
  public isPaid: boolean

  @column({ columnName: 'status' })
  public status: AppointmentStatus

  @column({ columnName: 'amount' })
  public amount: number | null

  @column({ columnName: 'description' })
  public description: string | null

  @belongsTo(() =>Patient,{
    foreignKey: 'patientId',
    localKey: 'id'
  })
  public patient : BelongsTo<typeof Patient>

  @belongsTo(()=>Soignant,{
    foreignKey: 'proId',
    localKey: 'id'
  })
  public pro : BelongsTo<typeof Soignant>

  @belongsTo(()=>MedicalFacilitator,{
    foreignKey: 'fmdId',
    localKey: 'id'
  })
  public fmd : BelongsTo<typeof MedicalFacilitator>

  @belongsTo(()=>HealthInstitute,{
    foreignKey: 'healthInstituteId',
    localKey: 'id'
  })
  public healthInstitute : BelongsTo<typeof HealthInstitute>

  @belongsTo(()=>AppointmentType,{
    foreignKey: 'appointmentTypeId',
    localKey: 'id'
  })
  public appointmentType : BelongsTo<typeof AppointmentType>

  @manyToMany(() => Service,{
    pivotTable: 'appointment_services',
    pivotForeignKey: 'appointment_id',
    pivotRelatedForeignKey: 'service_id',
    pivotTimestamps: true,
    pivotColumns: ['price']
  })
  public services: ManyToMany<typeof Service>

}
