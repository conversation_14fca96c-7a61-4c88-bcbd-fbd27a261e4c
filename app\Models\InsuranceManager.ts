import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import InsuranceCompany from './InsuranceCompany'
import InsuranceCompanyAgency from './InsuranceCompanyAgency'
import Country from './Country'
import City from './City'
import Quarter from './Quarter'

export default class InsuranceManager extends BaseModel {
  @column({ isPrimary: true, columnName: 'id' })
  public id: number

  @column({ columnName: 'user_id' })
  public user_id: number

  @column({ columnName: 'last_name' })
  public lastName: string

  @column({ columnName: 'first_name' })
  public firstName: string

  @column({ columnName: 'phone' })
  public phone: string

  @column({ columnName: 'email' })
  public email: string

  @column({ columnName: 'address' })
  public address: string | null

  @column({ columnName: 'country_id' })
  public countryId: number

  @column({ columnName: 'city_id' })
  public cityId: number  | null

  @column({ columnName: 'quarter_id' })
  public quarterId: number | null

  @column({ columnName: 'gender' })
  public gender: 'M' | 'F'

  @column({ columnName: 'birthday_year' })
  public birthday_year: number | null

  @column({ columnName: 'birthday_month' })
  public birthday_month: number | null

  @column({ columnName: 'birthday_day' })
  public birthday_day: number | null

  @column({ columnName: 'profession' })
  public profession: string | null

  @column({ columnName: 'insurance_company_id' })
  public insuranceCompanyId: number

  @column({ columnName: 'insurance_company_agency_id' })
  public insuranceCompanyAgencyId: number 

  @column({ columnName: 'type' })
  public type: string

  @column.dateTime({ autoCreate: true, columnName: 'created_at' })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true, columnName: 'updated_at' })
  public updatedAt: DateTime

  @belongsTo(() => InsuranceCompany,{
    localKey: 'id',
    foreignKey: 'insuranceCompanyId',
  })
  public company: BelongsTo<typeof InsuranceCompany>

  @belongsTo(() => InsuranceCompanyAgency,{
    localKey: 'id',
    foreignKey: 'insuranceCompanyAgencyId',
  })
  public agency: BelongsTo<typeof InsuranceCompanyAgency>

  @belongsTo(() => Country,{
    localKey: 'id',
    foreignKey: 'countryId',
  })
  public country: BelongsTo<typeof Country>

  @belongsTo(() => City,{
    localKey: 'id',
    foreignKey: 'cityId',
  })
  public city: BelongsTo<typeof City>

  @belongsTo(() => Quarter,{
    localKey: 'id',
    foreignKey: 'quarterId',
  })
  public quarter: BelongsTo<typeof Quarter>




}
