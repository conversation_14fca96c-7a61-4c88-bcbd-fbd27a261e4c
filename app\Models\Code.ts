import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import Patient from './Patient'
import Card from './Card'

export default class Code extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column({ columnName: 'patient_id' })
  public patientId: number

  @column({ columnName: 'card_id' })
  public cardId: number

  @column()
  public validity: string

  @column({ columnName: 'blocked_at' })
  public blockedAt: Date | null

  @column({ columnName: 'activated_at' })
  public activatedAt: Date | null

  @column({ columnName: 'expired_at' })
  public expiredAt: Date

  @column()
  public online_paid: boolean

  @belongsTo(() => Patient, {
    foreignKey: 'patientId',
    localKey: 'id'
  })
  public patient: BelongsTo<typeof Patient>

  @belongsTo(() => Card, {
    foreignKey: 'cardId',
    localKey: 'id'
  })
  public card: BelongsTo<typeof Card>
}
