import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class AnalyzeAskOrder extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column()
  public reference: string

  @column({ columnName: 'analyze_ask_id' })
  public analyzeAskId: number

  @column({ columnName: 'patient_id' })
  public patientId: number

  @column()
  public type: string

  @column()
  public image: string

  @column()
  public phone: string

  @column()
  public address: string

  @column()
  public priority: string

  @column()
  public location: string

  @column()
  public comment: string

  @column({ columnName: 'pharmacy_id' })
  public pharmacyId: number

  @column()
  public status: string
}
