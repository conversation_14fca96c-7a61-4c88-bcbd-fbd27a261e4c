import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import Package from './Package'
import Guarantee from './Guarantee'

export default class PackageGuarantee extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'package_id' })
  public packageId: number

  @column({ columnName: 'guarantee_id' })
  public guaranteeId: number

  @column({ columnName: 'coverage_rate' })
  public coverageRate: number | null

  @column({ columnName: 'coverage_limit' })
  public coverageLimit: number | null

  @column({ columnName: 'limit_type' })
  public limitType: 'none' | 'per_year' | 'per_visit' | 'per_lifetime' | 'per_condition' = 'none'

  @column({ columnName: 'limit_value' })
  public limitValue: number | null

  @column({ columnName: 'metadata' })
  public metadata: {
    requires_prescription?: boolean,
    age_min?: number,
    age_max?: number,
    preexisting_conditions?: boolean,
    allowed_providers?: string[],
    approval_required?: boolean
  } | null

  @column({ columnName: 'is_active' })
  public isActive: boolean = true

  @column()
  public description: string | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column.dateTime()
  public deletedAt: DateTime | null

  @belongsTo(() => Package, {
    foreignKey: 'packageId',
    localKey: 'id',
  })
  public package: BelongsTo<typeof Package>

  @belongsTo(() => Guarantee, {
    foreignKey: 'guaranteeId',
    localKey: 'id',
  })
  public guarantee: BelongsTo<typeof Guarantee>
}
