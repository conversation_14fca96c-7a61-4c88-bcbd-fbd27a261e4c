import { DateTime } from 'luxon'
import { BaseModel, column, HasMany, hasMany } from '@ioc:Adonis/Lucid/Orm'
import Guarantee from './Guarantee'

export default class GuaranteeType extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public name: string

  @column()
  public code: string | null

  @column()
  public description: string | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column({ columnName: 'is_active' })
  public isActive: boolean = true

  @hasMany(() => Guarantee, {
    foreignKey: 'guaranteeTypeId',
    localKey: 'id',
  })
  public guarantees: HasMany<typeof Guarantee>
}
